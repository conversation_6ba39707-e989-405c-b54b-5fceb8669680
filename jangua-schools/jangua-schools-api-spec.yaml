openapi: 3.0.3
info:
  title: Jangua Schools Platform API
  description: |
    Comprehensive API for the Jangua Schools educational management platform.
    Supports multi-institutional management including universities, high schools, and elementary schools.
  version: 1.0.0
  contact:
    name: Jangua Schools API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.jangua-schools.com/v1
    description: Production server
  - url: https://staging-api.jangua-schools.com/v1
    description: Staging server
  - url: http://localhost:8000/v1
    description: Development server

security:
  - bearerAuth: []

paths:
  # Authentication & Authorization
  /auth/login:
    post:
      tags: [Authentication]
      summary: User login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
              required: [email, password]
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
                  expires_in:
                    type: integer

  /auth/logout:
    post:
      tags: [Authentication]
      summary: User logout
      responses:
        '200':
          description: Logout successful

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      responses:
        '200':
          description: Token refreshed successfully

  # Institution Management
  /institutions:
    get:
      tags: [Institutions]
      summary: List all institutions
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [university, high-school, school]
        - name: status
          in: query
          schema:
            type: string
            enum: [active, pending, suspended]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: List of institutions
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Institution'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      tags: [Institutions]
      summary: Create new institution
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInstitutionRequest'
      responses:
        '201':
          description: Institution created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Institution'

  /institutions/{institutionId}:
    get:
      tags: [Institutions]
      summary: Get institution by ID
      parameters:
        - name: institutionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Institution details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Institution'

    put:
      tags: [Institutions]
      summary: Update institution
      parameters:
        - name: institutionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInstitutionRequest'
      responses:
        '200':
          description: Institution updated successfully

    delete:
      tags: [Institutions]
      summary: Delete institution
      parameters:
        - name: institutionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Institution deleted successfully

  # User Management
  /users:
    get:
      tags: [Users]
      summary: List users
      parameters:
        - name: role
          in: query
          schema:
            type: string
            enum: [super_admin, institution_admin, faculty, staff, student, parent]
        - name: institution_id
          in: query
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended]
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      tags: [Users]
      summary: Create new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully

  /users/{userId}:
    get:
      tags: [Users]
      summary: Get user by ID
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

  # Student Management
  /students:
    get:
      tags: [Students]
      summary: List students
      parameters:
        - name: institution_id
          in: query
          schema:
            type: string
            format: uuid
        - name: grade
          in: query
          schema:
            type: string
        - name: section
          in: query
          schema:
            type: string
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, graduated, transferred]
      responses:
        '200':
          description: List of students
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Student'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      tags: [Students]
      summary: Register new student
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStudentRequest'
      responses:
        '201':
          description: Student registered successfully

  /students/{studentId}:
    get:
      tags: [Students]
      summary: Get student by ID
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Student details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'

  # Academic Management
  /academic-years:
    get:
      tags: [Academic Management]
      summary: List academic years
      parameters:
        - name: institution_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of academic years
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AcademicYear'

    post:
      tags: [Academic Management]
      summary: Create academic year
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAcademicYearRequest'
      responses:
        '201':
          description: Academic year created successfully

  /semesters:
    get:
      tags: [Academic Management]
      summary: List semesters
      parameters:
        - name: academic_year_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of semesters
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Semester'

  /departments:
    get:
      tags: [Academic Management]
      summary: List departments
      parameters:
        - name: institution_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of departments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Department'

  # Attendance Management
  /attendance:
    get:
      tags: [Attendance]
      summary: Get attendance records
      parameters:
        - name: student_id
          in: query
          schema:
            type: string
            format: uuid
        - name: class_id
          in: query
          schema:
            type: string
            format: uuid
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Attendance records
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AttendanceRecord'

    post:
      tags: [Attendance]
      summary: Record attendance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAttendanceRequest'
      responses:
        '201':
          description: Attendance recorded successfully

  # Grading & Reporting
  /grades:
    get:
      tags: [Grading]
      summary: Get student grades
      parameters:
        - name: student_id
          in: query
          schema:
            type: string
            format: uuid
        - name: subject_id
          in: query
          schema:
            type: string
            format: uuid
        - name: semester_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Student grades
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Grade'

    post:
      tags: [Grading]
      summary: Record grade
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateGradeRequest'
      responses:
        '201':
          description: Grade recorded successfully

  /report-cards/{studentId}:
    get:
      tags: [Grading]
      summary: Generate student report card
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: semester_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Student report card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportCard'

  # Financial Management
  /payments:
    get:
      tags: [Financial]
      summary: List payments
      parameters:
        - name: student_id
          in: query
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, paid, overdue, cancelled]
        - name: type
          in: query
          schema:
            type: string
            enum: [tuition, fees, books, transport, other]
      responses:
        '200':
          description: List of payments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Payment'

    post:
      tags: [Financial]
      summary: Create payment record
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentRequest'
      responses:
        '201':
          description: Payment record created

  /payments/{paymentId}/process:
    post:
      tags: [Financial]
      summary: Process payment
      parameters:
        - name: paymentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProcessPaymentRequest'
      responses:
        '200':
          description: Payment processed successfully

  # Communication
  /messages:
    get:
      tags: [Communication]
      summary: Get messages
      parameters:
        - name: recipient_id
          in: query
          schema:
            type: string
            format: uuid
        - name: sender_id
          in: query
          schema:
            type: string
            format: uuid
        - name: type
          in: query
          schema:
            type: string
            enum: [direct, announcement, alert]
      responses:
        '200':
          description: List of messages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Message'

    post:
      tags: [Communication]
      summary: Send message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMessageRequest'
      responses:
        '201':
          description: Message sent successfully

  /notifications:
    get:
      tags: [Communication]
      summary: Get notifications
      parameters:
        - name: user_id
          in: query
          schema:
            type: string
            format: uuid
        - name: type
          in: query
          schema:
            type: string
            enum: [payment, grade, attendance, event, system]
        - name: read
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'

  /notifications/{notificationId}/mark-read:
    patch:
      tags: [Communication]
      summary: Mark notification as read
      parameters:
        - name: notificationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Notification marked as read

  # Scheduling
  /schedules:
    get:
      tags: [Scheduling]
      summary: Get class schedules
      parameters:
        - name: class_id
          in: query
          schema:
            type: string
            format: uuid
        - name: teacher_id
          in: query
          schema:
            type: string
            format: uuid
        - name: student_id
          in: query
          schema:
            type: string
            format: uuid
        - name: date
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Class schedules
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Schedule'

    post:
      tags: [Scheduling]
      summary: Create schedule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScheduleRequest'
      responses:
        '201':
          description: Schedule created successfully

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        role:
          type: string
          enum: [super_admin, institution_admin, faculty, staff, student, parent]
        institution_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [active, inactive, suspended]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Institution:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        type:
          type: string
          enum: [university, high-school, school]
        location:
          type: string
        established:
          type: string
          format: date
        status:
          type: string
          enum: [active, pending, suspended]
        total_students:
          type: integer
        total_faculty:
          type: integer
        subscription_plan:
          type: string
          enum: [basic, standard, premium]
        created_at:
          type: string
          format: date-time

    Student:
      type: object
      properties:
        id:
          type: string
          format: uuid
        student_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        date_of_birth:
          type: string
          format: date
        grade:
          type: string
        section:
          type: string
        institution_id:
          type: string
          format: uuid
        parent_id:
          type: string
          format: uuid
        enrollment_date:
          type: string
          format: date
        status:
          type: string
          enum: [active, inactive, graduated, transferred]
        gpa:
          type: number
          format: float
        attendance_rate:
          type: number
          format: float

    Pagination:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        total_pages:
          type: integer

    CreateInstitutionRequest:
      type: object
      required: [name, type, location]
      properties:
        name:
          type: string
        type:
          type: string
          enum: [university, high-school, school]
        location:
          type: string
        description:
          type: string

    CreateUserRequest:
      type: object
      required: [email, first_name, last_name, role]
      properties:
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        role:
          type: string
          enum: [institution_admin, faculty, staff, student, parent]
        institution_id:
          type: string
          format: uuid

    CreateStudentRequest:
      type: object
      required: [first_name, last_name, email, grade, institution_id]
      properties:
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        date_of_birth:
          type: string
          format: date
        grade:
          type: string
        section:
          type: string
        institution_id:
          type: string
          format: uuid
        parent_email:
          type: string
          format: email

    AcademicYear:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        institution_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [active, completed, upcoming]

    Semester:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        academic_year_id:
          type: string
          format: uuid
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        status:
          type: string
          enum: [active, completed, upcoming]

    Department:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        head_id:
          type: string
          format: uuid
        institution_id:
          type: string
          format: uuid
        budget:
          type: number
          format: float
        total_faculty:
          type: integer
        total_students:
          type: integer

    AttendanceRecord:
      type: object
      properties:
        id:
          type: string
          format: uuid
        student_id:
          type: string
          format: uuid
        class_id:
          type: string
          format: uuid
        date:
          type: string
          format: date
        status:
          type: string
          enum: [present, absent, late, excused]
        notes:
          type: string
        recorded_by:
          type: string
          format: uuid

    Grade:
      type: object
      properties:
        id:
          type: string
          format: uuid
        student_id:
          type: string
          format: uuid
        subject_id:
          type: string
          format: uuid
        semester_id:
          type: string
          format: uuid
        grade:
          type: string
        points:
          type: number
          format: float
        max_points:
          type: number
          format: float
        type:
          type: string
          enum: [assignment, quiz, exam, project, final]
        date:
          type: string
          format: date

    Payment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        student_id:
          type: string
          format: uuid
        amount:
          type: number
          format: float
        currency:
          type: string
          default: USD
        type:
          type: string
          enum: [tuition, fees, books, transport, other]
        description:
          type: string
        due_date:
          type: string
          format: date
        paid_date:
          type: string
          format: date
        status:
          type: string
          enum: [pending, paid, overdue, cancelled]

    Message:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sender_id:
          type: string
          format: uuid
        recipient_id:
          type: string
          format: uuid
        subject:
          type: string
        content:
          type: string
        type:
          type: string
          enum: [direct, announcement, alert]
        read:
          type: boolean
        sent_at:
          type: string
          format: date-time

    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user_id:
          type: string
          format: uuid
        title:
          type: string
        message:
          type: string
        type:
          type: string
          enum: [payment, grade, attendance, event, system]
        read:
          type: boolean
        urgent:
          type: boolean
        created_at:
          type: string
          format: date-time

    Schedule:
      type: object
      properties:
        id:
          type: string
          format: uuid
        class_id:
          type: string
          format: uuid
        subject_id:
          type: string
          format: uuid
        teacher_id:
          type: string
          format: uuid
        room:
          type: string
        day_of_week:
          type: integer
          minimum: 1
          maximum: 7
        start_time:
          type: string
          format: time
        end_time:
          type: string
          format: time
        semester_id:
          type: string
          format: uuid

    CreateAcademicYearRequest:
      type: object
      required: [name, start_date, end_date, institution_id]
      properties:
        name:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        institution_id:
          type: string
          format: uuid

    CreateAttendanceRequest:
      type: object
      required: [student_id, class_id, date, status]
      properties:
        student_id:
          type: string
          format: uuid
        class_id:
          type: string
          format: uuid
        date:
          type: string
          format: date
        status:
          type: string
          enum: [present, absent, late, excused]
        notes:
          type: string

    CreateGradeRequest:
      type: object
      required: [student_id, subject_id, grade, points, max_points]
      properties:
        student_id:
          type: string
          format: uuid
        subject_id:
          type: string
          format: uuid
        semester_id:
          type: string
          format: uuid
        grade:
          type: string
        points:
          type: number
          format: float
        max_points:
          type: number
          format: float
        type:
          type: string
          enum: [assignment, quiz, exam, project, final]

    CreatePaymentRequest:
      type: object
      required: [student_id, amount, type, description, due_date]
      properties:
        student_id:
          type: string
          format: uuid
        amount:
          type: number
          format: float
        type:
          type: string
          enum: [tuition, fees, books, transport, other]
        description:
          type: string
        due_date:
          type: string
          format: date

    CreateMessageRequest:
      type: object
      required: [recipient_id, subject, content]
      properties:
        recipient_id:
          type: string
          format: uuid
        subject:
          type: string
        content:
          type: string
        type:
          type: string
          enum: [direct, announcement, alert]
          default: direct

    CreateScheduleRequest:
      type: object
      required: [class_id, subject_id, teacher_id, day_of_week, start_time, end_time]
      properties:
        class_id:
          type: string
          format: uuid
        subject_id:
          type: string
          format: uuid
        teacher_id:
          type: string
          format: uuid
        room:
          type: string
        day_of_week:
          type: integer
          minimum: 1
          maximum: 7
        start_time:
          type: string
          format: time
        end_time:
          type: string
          format: time
        semester_id:
          type: string
          format: uuid

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Institutions
    description: Institution management operations
  - name: Users
    description: User management operations
  - name: Students
    description: Student management operations
  - name: Academic Management
    description: Academic year, semester, and department management
  - name: Attendance
    description: Attendance tracking and management
  - name: Grading
    description: Grade management and reporting
  - name: Financial
    description: Payment and financial management
  - name: Communication
    description: Messaging and notifications
  - name: Scheduling
    description: Class scheduling and timetables
