"use client";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import Solutions from "@/components/Solutions";
import Footer from "@/components/Footer";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="min-h-screen">
      <Header />
      <Hero />
      <Features />
      <Solutions />

      {/* Test Button Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Platform Demo</h2>
          <p className="text-gray-600 mb-8">
            Explore the Jangua Schools platform features and UI components
          </p>
          <Link href="/test-pages">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              Test Platform Features
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
}
