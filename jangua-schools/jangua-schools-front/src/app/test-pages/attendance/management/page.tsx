"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { 
  ArrowLeft, 
  Search, 
  Calendar as CalendarIcon,
  Save,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  BarChart3
} from "lucide-react";

export default function AttendanceManagement() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedClass, setSelectedClass] = useState("10A");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data for classes
  const classes = [
    { id: "10A", name: "Grade 10 - Section A", students: 30 },
    { id: "10B", name: "Grade 10 - Section B", students: 28 },
    { id: "11S1", name: "Grade 11 - Science 1", students: 25 },
    { id: "11S2", name: "Grade 11 - Science 2", students: 27 },
    { id: "12L1", name: "Grade 12 - Literature 1", students: 22 }
  ];

  // Mock data for students with attendance
  const students = [
    {
      id: 1,
      studentId: "STU001",
      name: "Ahmed Benali",
      status: "present",
      arrivalTime: "08:00",
      notes: ""
    },
    {
      id: 2,
      studentId: "STU002",
      name: "Fatima El-Mansouri",
      status: "present",
      arrivalTime: "08:05",
      notes: ""
    },
    {
      id: 3,
      studentId: "STU003",
      name: "Youssef Alami",
      status: "late",
      arrivalTime: "08:30",
      notes: "Traffic delay"
    },
    {
      id: 4,
      studentId: "STU004",
      name: "Khadija Benkirane",
      status: "absent",
      arrivalTime: "",
      notes: "Sick leave"
    },
    {
      id: 5,
      studentId: "STU005",
      name: "Omar Zaki",
      status: "present",
      arrivalTime: "07:55",
      notes: ""
    },
    {
      id: 6,
      studentId: "STU006",
      name: "Aicha Benali",
      status: "excused",
      arrivalTime: "",
      notes: "Medical appointment"
    }
  ];

  const [attendanceData, setAttendanceData] = useState(students);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present":
        return "bg-green-100 text-green-800";
      case "absent":
        return "bg-red-100 text-red-800";
      case "late":
        return "bg-yellow-100 text-yellow-800";
      case "excused":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "absent":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "late":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "excused":
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default:
        return <XCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const updateAttendance = (studentId: number, field: string, value: string) => {
    setAttendanceData(prev => 
      prev.map(student => 
        student.id === studentId 
          ? { ...student, [field]: value }
          : student
      )
    );
  };

  const filteredStudents = attendanceData.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.studentId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const attendanceStats = {
    present: attendanceData.filter(s => s.status === "present").length,
    absent: attendanceData.filter(s => s.status === "absent").length,
    late: attendanceData.filter(s => s.status === "late").length,
    excused: attendanceData.filter(s => s.status === "excused").length,
    total: attendanceData.length
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Attendance Management</h1>
              <p className="text-gray-600">Track and manage student attendance</p>
            </div>
            <Button>
              <Save className="w-4 h-4 mr-2" />
              Save Attendance
            </Button>
          </div>
        </div>

        {/* Controls */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Select Class</label>
                <Select value={selectedClass} onValueChange={setSelectedClass}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name} ({cls.students} students)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full sm:w-48">
                      <CalendarIcon className="w-4 h-4 mr-2" />
                      {format(selectedDate, "PPP")}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={(date) => date && setSelectedDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Search Students</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search by name or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold">{attendanceStats.total}</p>
                </div>
                <Users className="w-8 h-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Present</p>
                  <p className="text-2xl font-bold text-green-600">{attendanceStats.present}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Absent</p>
                  <p className="text-2xl font-bold text-red-600">{attendanceStats.absent}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Late</p>
                  <p className="text-2xl font-bold text-yellow-600">{attendanceStats.late}</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Excused</p>
                  <p className="text-2xl font-bold text-blue-600">{attendanceStats.excused}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance List */}
        <Card>
          <CardHeader>
            <CardTitle>Student Attendance - {selectedClass}</CardTitle>
            <CardDescription>
              Mark attendance for {format(selectedDate, "EEEE, MMMM d, yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredStudents.map((student) => (
                <div key={student.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex items-center gap-3 flex-1">
                    {getStatusIcon(student.status)}
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-gray-600">ID: {student.studentId}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div>
                      <label className="text-sm font-medium mb-1 block">Status</label>
                      <Select 
                        value={student.status} 
                        onValueChange={(value) => updateAttendance(student.id, "status", value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="present">Present</SelectItem>
                          <SelectItem value="absent">Absent</SelectItem>
                          <SelectItem value="late">Late</SelectItem>
                          <SelectItem value="excused">Excused</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {(student.status === "present" || student.status === "late") && (
                      <div>
                        <label className="text-sm font-medium mb-1 block">Arrival Time</label>
                        <Input
                          type="time"
                          value={student.arrivalTime}
                          onChange={(e) => updateAttendance(student.id, "arrivalTime", e.target.value)}
                          className="w-32"
                        />
                      </div>
                    )}
                    
                    <div>
                      <label className="text-sm font-medium mb-1 block">Notes</label>
                      <Input
                        placeholder="Add notes..."
                        value={student.notes}
                        onChange={(e) => updateAttendance(student.id, "notes", e.target.value)}
                        className="w-40"
                      />
                    </div>
                  </div>
                  
                  <Badge className={getStatusColor(student.status)}>
                    {student.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
