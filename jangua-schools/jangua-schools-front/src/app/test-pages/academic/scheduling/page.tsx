"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  ArrowLeft, 
  Plus, 
  Calendar,
  Clock,
  Users,
  BookOpen,
  MapPin,
  Edit,
  Eye
} from "lucide-react";

export default function CourseScheduling() {
  const [selectedGrade, setSelectedGrade] = useState("10");
  const [selectedDay, setSelectedDay] = useState("monday");

  // Mock data for time slots
  const timeSlots = [
    "08:00 - 08:45",
    "08:45 - 09:30",
    "09:30 - 10:15",
    "10:15 - 10:30", // Break
    "10:30 - 11:15",
    "11:15 - 12:00",
    "12:00 - 12:45",
    "12:45 - 13:30", // Lunch
    "13:30 - 14:15",
    "14:15 - 15:00"
  ];

  // Mock data for schedule
  const schedule = {
    monday: [
      { time: "08:00 - 08:45", subject: "Mathematics", teacher: "Dr. Ahmed Benali", room: "Room 101", class: "10A" },
      { time: "08:45 - 09:30", subject: "Physics", teacher: "Dr. Fatima El-Mansouri", room: "Lab 1", class: "10A" },
      { time: "09:30 - 10:15", subject: "English", teacher: "Ms. Sarah Johnson", room: "Room 203", class: "10A" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", class: "" },
      { time: "10:30 - 11:15", subject: "Chemistry", teacher: "Dr. Omar Zaki", room: "Lab 2", class: "10A" },
      { time: "11:15 - 12:00", subject: "Arabic", teacher: "Mr. Hassan Alami", room: "Room 105", class: "10A" },
      { time: "12:00 - 12:45", subject: "History", teacher: "Ms. Aicha Benkirane", room: "Room 201", class: "10A" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", class: "" },
      { time: "13:30 - 14:15", subject: "Physical Education", teacher: "Mr. Youssef Alami", room: "Gym", class: "10A" },
      { time: "14:15 - 15:00", subject: "Art", teacher: "Ms. Khadija Benkirane", room: "Art Room", class: "10A" }
    ],
    tuesday: [
      { time: "08:00 - 08:45", subject: "Physics", teacher: "Dr. Fatima El-Mansouri", room: "Lab 1", class: "10A" },
      { time: "08:45 - 09:30", subject: "Mathematics", teacher: "Dr. Ahmed Benali", room: "Room 101", class: "10A" },
      { time: "09:30 - 10:15", subject: "Biology", teacher: "Dr. Meryem Benali", room: "Lab 3", class: "10A" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", class: "" },
      { time: "10:30 - 11:15", subject: "French", teacher: "Ms. Laila Zaki", room: "Room 204", class: "10A" },
      { time: "11:15 - 12:00", subject: "Geography", teacher: "Mr. Rachid Alami", room: "Room 202", class: "10A" },
      { time: "12:00 - 12:45", subject: "English", teacher: "Ms. Sarah Johnson", room: "Room 203", class: "10A" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", class: "" },
      { time: "13:30 - 14:15", subject: "Computer Science", teacher: "Mr. Karim Benali", room: "Computer Lab", class: "10A" },
      { time: "14:15 - 15:00", subject: "Music", teacher: "Ms. Nadia El-Mansouri", room: "Music Room", class: "10A" }
    ]
  };

  // Mock data for teachers
  const teachers = [
    { id: 1, name: "Dr. Ahmed Benali", subject: "Mathematics", availability: "Full Time" },
    { id: 2, name: "Dr. Fatima El-Mansouri", subject: "Physics", availability: "Full Time" },
    { id: 3, name: "Ms. Sarah Johnson", subject: "English", availability: "Part Time" },
    { id: 4, name: "Dr. Omar Zaki", subject: "Chemistry", availability: "Full Time" },
    { id: 5, name: "Mr. Hassan Alami", subject: "Arabic", availability: "Full Time" }
  ];

  // Mock data for rooms
  const rooms = [
    { id: 1, name: "Room 101", type: "Classroom", capacity: 30, equipment: ["Projector", "Whiteboard"] },
    { id: 2, name: "Lab 1", type: "Science Lab", capacity: 25, equipment: ["Lab Equipment", "Safety Gear"] },
    { id: 3, name: "Computer Lab", type: "Computer Lab", capacity: 20, equipment: ["Computers", "Projector"] },
    { id: 4, name: "Gym", type: "Gymnasium", capacity: 50, equipment: ["Sports Equipment"] },
    { id: 5, name: "Art Room", type: "Art Studio", capacity: 25, equipment: ["Art Supplies", "Easels"] }
  ];

  const days = [
    { value: "monday", label: "Monday" },
    { value: "tuesday", label: "Tuesday" },
    { value: "wednesday", label: "Wednesday" },
    { value: "thursday", label: "Thursday" },
    { value: "friday", label: "Friday" }
  ];

  const grades = [
    { value: "9", label: "Grade 9" },
    { value: "10", label: "Grade 10" },
    { value: "11", label: "Grade 11" },
    { value: "12", label: "Grade 12" }
  ];

  const getSubjectColor = (subject: string) => {
    const colors: { [key: string]: string } = {
      "Mathematics": "bg-blue-100 text-blue-800",
      "Physics": "bg-purple-100 text-purple-800",
      "Chemistry": "bg-green-100 text-green-800",
      "Biology": "bg-emerald-100 text-emerald-800",
      "English": "bg-yellow-100 text-yellow-800",
      "Arabic": "bg-orange-100 text-orange-800",
      "French": "bg-pink-100 text-pink-800",
      "History": "bg-red-100 text-red-800",
      "Geography": "bg-indigo-100 text-indigo-800",
      "Physical Education": "bg-teal-100 text-teal-800",
      "Art": "bg-rose-100 text-rose-800",
      "Music": "bg-violet-100 text-violet-800",
      "Computer Science": "bg-cyan-100 text-cyan-800"
    };
    return colors[subject] || "bg-gray-100 text-gray-800";
  };

  const currentSchedule = schedule[selectedDay as keyof typeof schedule] || [];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Course Scheduling</h1>
              <p className="text-gray-600">Manage class timetables and scheduling</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Class
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Schedule New Class</DialogTitle>
                  <DialogDescription>
                    Add a new class to the timetable
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="subject">Subject</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mathematics">Mathematics</SelectItem>
                        <SelectItem value="physics">Physics</SelectItem>
                        <SelectItem value="chemistry">Chemistry</SelectItem>
                        <SelectItem value="biology">Biology</SelectItem>
                        <SelectItem value="english">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="teacher">Teacher</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        {teachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id.toString()}>
                            {teacher.name} - {teacher.subject}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="room">Room</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select room" />
                      </SelectTrigger>
                      <SelectContent>
                        {rooms.map((room) => (
                          <SelectItem key={room.id} value={room.id.toString()}>
                            {room.name} - {room.type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">Start Time</Label>
                      <Input id="startTime" type="time" />
                    </div>
                    <div>
                      <Label htmlFor="endTime">End Time</Label>
                      <Input id="endTime" type="time" />
                    </div>
                  </div>
                  <Button className="w-full">Schedule Class</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Controls */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Grade</label>
                <Select value={selectedGrade} onValueChange={setSelectedGrade}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select grade" />
                  </SelectTrigger>
                  <SelectContent>
                    {grades.map((grade) => (
                      <SelectItem key={grade.value} value={grade.value}>
                        {grade.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Day</label>
                <Select value={selectedDay} onValueChange={setSelectedDay}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {days.map((day) => (
                      <SelectItem key={day.value} value={day.value}>
                        {day.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Schedule Table */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Grade {selectedGrade} - {days.find(d => d.value === selectedDay)?.label} Schedule
                </CardTitle>
                <CardDescription>Class timetable for the selected day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {currentSchedule.map((slot, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      {slot.subject === "Break" || slot.subject === "Lunch Break" ? (
                        <div className="flex items-center justify-center py-2">
                          <div className="text-center">
                            <p className="font-medium text-gray-600">{slot.subject}</p>
                            <p className="text-sm text-gray-500">{slot.time}</p>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <p className="text-sm font-medium">{slot.time}</p>
                              <Clock className="w-4 h-4 text-gray-400 mx-auto mt-1" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge className={getSubjectColor(slot.subject)}>
                                  {slot.subject}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {slot.teacher}
                              </p>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {slot.room}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Teachers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Available Teachers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {teachers.map((teacher) => (
                    <div key={teacher.id} className="border rounded-lg p-3">
                      <p className="font-medium">{teacher.name}</p>
                      <p className="text-sm text-gray-600">{teacher.subject}</p>
                      <Badge variant="outline" className="text-xs mt-1">
                        {teacher.availability}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Rooms */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Available Rooms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {rooms.map((room) => (
                    <div key={room.id} className="border rounded-lg p-3">
                      <p className="font-medium">{room.name}</p>
                      <p className="text-sm text-gray-600">{room.type}</p>
                      <p className="text-xs text-gray-500">Capacity: {room.capacity}</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {room.equipment.map((eq, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {eq}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Weekly Overview */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Weekly Overview - Grade {selectedGrade}
            </CardTitle>
            <CardDescription>Complete week schedule at a glance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Time</th>
                    {days.map((day) => (
                      <th key={day.value} className="text-center p-3 font-medium">
                        {day.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {timeSlots.slice(0, 7).map((time, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-3 font-medium text-sm">{time}</td>
                      {days.map((day) => {
                        const daySchedule = schedule[day.value as keyof typeof schedule] || [];
                        const classAtTime = daySchedule.find(slot => slot.time === time);
                        return (
                          <td key={day.value} className="p-3 text-center">
                            {classAtTime ? (
                              <div className="text-xs">
                                <Badge className={getSubjectColor(classAtTime.subject)} variant="outline">
                                  {classAtTime.subject}
                                </Badge>
                                <p className="text-gray-600 mt-1">{classAtTime.room}</p>
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
