"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { 
  ArrowLeft, 
  Plus, 
  Calendar as CalendarIcon,
  GraduationCap,
  Users,
  BookOpen,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Eye
} from "lucide-react";

export default function AcademicYearManagement() {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  // Mock data for academic years
  const academicYears = [
    {
      id: 1,
      name: "Academic Year 2024-2025",
      startDate: "2024-09-01",
      endDate: "2025-06-30",
      status: "active",
      totalStudents: 1200,
      totalSemesters: 2,
      currentSemester: "Fall 2024",
      progress: 45,
      description: "Current academic year with full enrollment"
    },
    {
      id: 2,
      name: "Academic Year 2023-2024",
      startDate: "2023-09-01",
      endDate: "2024-06-30",
      status: "completed",
      totalStudents: 1150,
      totalSemesters: 2,
      currentSemester: "Completed",
      progress: 100,
      description: "Successfully completed academic year"
    },
    {
      id: 3,
      name: "Academic Year 2025-2026",
      startDate: "2025-09-01",
      endDate: "2026-06-30",
      status: "planned",
      totalStudents: 0,
      totalSemesters: 2,
      currentSemester: "Not Started",
      progress: 0,
      description: "Upcoming academic year in planning phase"
    }
  ];

  // Mock data for semesters in current year
  const semesters = [
    {
      id: 1,
      name: "Fall 2024",
      startDate: "2024-09-01",
      endDate: "2025-01-15",
      status: "active",
      totalWeeks: 18,
      completedWeeks: 8,
      subjects: 12,
      examsScheduled: 24
    },
    {
      id: 2,
      name: "Spring 2025",
      startDate: "2025-01-20",
      endDate: "2025-06-30",
      status: "upcoming",
      totalWeeks: 18,
      completedWeeks: 0,
      subjects: 12,
      examsScheduled: 0
    }
  ];

  // Mock data for key dates
  const keyDates = [
    { id: 1, title: "Fall Semester Begins", date: "2024-09-01", type: "semester" },
    { id: 2, title: "Mid-term Exams", date: "2024-11-15", type: "exam" },
    { id: 3, title: "Winter Break", date: "2024-12-20", type: "holiday" },
    { id: 4, title: "Spring Semester Begins", date: "2025-01-20", type: "semester" },
    { id: 5, title: "Final Exams", date: "2025-06-01", type: "exam" },
    { id: 6, title: "Graduation Ceremony", date: "2025-06-30", type: "event" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "planned":
        return "bg-yellow-100 text-yellow-800";
      case "upcoming":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "completed":
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case "planned":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "upcoming":
        return <AlertCircle className="w-4 h-4 text-purple-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "semester":
        return "bg-blue-100 text-blue-800";
      case "exam":
        return "bg-red-100 text-red-800";
      case "holiday":
        return "bg-green-100 text-green-800";
      case "event":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Academic Year Management</h1>
              <p className="text-gray-600">Manage academic years and terms</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Academic Year
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Academic Year</DialogTitle>
                  <DialogDescription>
                    Set up a new academic year with start and end dates
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="yearName">Academic Year Name</Label>
                    <Input id="yearName" placeholder="e.g., Academic Year 2025-2026" />
                  </div>
                  <div>
                    <Label>Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start">
                          <CalendarIcon className="w-4 h-4 mr-2" />
                          {startDate ? format(startDate, "PPP") : "Select start date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label>End Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start">
                          <CalendarIcon className="w-4 h-4 mr-2" />
                          {endDate ? format(endDate, "PPP") : "Select end date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Input id="description" placeholder="Brief description of the academic year" />
                  </div>
                  <Button className="w-full">Create Academic Year</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Academic Years List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {academicYears.map((year) => (
            <Card key={year.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl">{year.name}</CardTitle>
                    <CardDescription>{year.description}</CardDescription>
                  </div>
                  <Badge className={getStatusColor(year.status)}>
                    {year.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Start Date:</span>
                      <p className="font-medium">{year.startDate}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">End Date:</span>
                      <p className="font-medium">{year.endDate}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Students:</span>
                      <p className="font-medium">{year.totalStudents.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Semesters:</span>
                      <p className="font-medium">{year.totalSemesters}</p>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-500">Progress:</span>
                      <span className="text-sm font-medium">{year.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${year.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <span className="text-gray-500 text-sm">Current Status:</span>
                    <p className="font-medium">{year.currentSemester}</p>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Year Semesters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Current Year Semesters
              </CardTitle>
              <CardDescription>Academic Year 2024-2025 semester breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {semesters.map((semester) => (
                  <div key={semester.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(semester.status)}
                        <h4 className="font-medium">{semester.name}</h4>
                      </div>
                      <Badge className={getStatusColor(semester.status)}>
                        {semester.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-500">Start:</span>
                        <p className="font-medium">{semester.startDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">End:</span>
                        <p className="font-medium">{semester.endDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Weeks:</span>
                        <p className="font-medium">{semester.completedWeeks}/{semester.totalWeeks}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Subjects:</span>
                        <p className="font-medium">{semester.subjects}</p>
                      </div>
                    </div>
                    {semester.status === "active" && (
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-xs text-gray-500">Progress:</span>
                          <span className="text-xs font-medium">
                            {Math.round((semester.completedWeeks / semester.totalWeeks) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5">
                          <div 
                            className="bg-green-600 h-1.5 rounded-full" 
                            style={{ width: `${(semester.completedWeeks / semester.totalWeeks) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Key Academic Dates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="w-5 h-5" />
                Key Academic Dates
              </CardTitle>
              <CardDescription>Important dates for Academic Year 2024-2025</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {keyDates.map((date) => (
                  <div key={date.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{date.title}</p>
                      <p className="text-sm text-gray-600">{date.date}</p>
                    </div>
                    <Badge className={getTypeColor(date.type)}>
                      {date.type}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Academic Year Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">1,200</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Semesters</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
                <BookOpen className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed Weeks</p>
                  <p className="text-2xl font-bold">8</p>
                </div>
                <Clock className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Year Progress</p>
                  <p className="text-2xl font-bold">45%</p>
                </div>
                <GraduationCap className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
