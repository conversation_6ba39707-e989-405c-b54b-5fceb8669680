"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  BookOpen,
  Users,
  BarChart3,
  TrendingUp,
  Save,
  Edit,
  Eye,
  Award
} from "lucide-react";

export default function GradeManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedClass, setSelectedClass] = useState("10A");
  const [selectedSubject, setSelectedSubject] = useState("mathematics");

  // Mock data for classes
  const classes = [
    { id: "10A", name: "Grade 10 - Section A", students: 30 },
    { id: "10B", name: "Grade 10 - Section B", students: 28 },
    { id: "11S1", name: "Grade 11 - Science 1", students: 25 },
    { id: "11S2", name: "Grade 11 - Science 2", students: 27 }
  ];

  // Mock data for subjects
  const subjects = [
    { id: "mathematics", name: "Mathematics", teacher: "Dr. Ahmed Benali" },
    { id: "physics", name: "Physics", teacher: "Dr. Fatima El-Mansouri" },
    { id: "chemistry", name: "Chemistry", teacher: "Dr. Omar Zaki" },
    { id: "biology", name: "Biology", teacher: "Dr. Aicha Benkirane" }
  ];

  // Mock data for students with grades
  const students = [
    {
      id: 1,
      studentId: "STU001",
      name: "Ahmed Benali",
      grades: {
        assignment1: 85,
        assignment2: 92,
        quiz1: 78,
        quiz2: 88,
        midterm: 82,
        final: null
      },
      average: 85.0,
      letterGrade: "B+"
    },
    {
      id: 2,
      studentId: "STU002",
      name: "Fatima El-Mansouri",
      grades: {
        assignment1: 95,
        assignment2: 98,
        quiz1: 92,
        quiz2: 94,
        midterm: 96,
        final: null
      },
      average: 95.0,
      letterGrade: "A"
    },
    {
      id: 3,
      studentId: "STU003",
      name: "Youssef Alami",
      grades: {
        assignment1: 72,
        assignment2: 78,
        quiz1: 65,
        quiz2: 70,
        midterm: 75,
        final: null
      },
      average: 72.0,
      letterGrade: "C+"
    },
    {
      id: 4,
      studentId: "STU004",
      name: "Khadija Benkirane",
      grades: {
        assignment1: 88,
        assignment2: 90,
        quiz1: 85,
        quiz2: 87,
        midterm: 89,
        final: null
      },
      average: 87.8,
      letterGrade: "B+"
    }
  ];

  // Mock data for grade categories
  const gradeCategories = [
    { name: "Assignment 1", weight: 15, maxPoints: 100, type: "assignment" },
    { name: "Assignment 2", weight: 15, maxPoints: 100, type: "assignment" },
    { name: "Quiz 1", weight: 10, maxPoints: 100, type: "quiz" },
    { name: "Quiz 2", weight: 10, maxPoints: 100, type: "quiz" },
    { name: "Midterm Exam", weight: 25, maxPoints: 100, type: "exam" },
    { name: "Final Exam", weight: 25, maxPoints: 100, type: "exam" }
  ];

  const [gradeData, setGradeData] = useState(students);

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return "text-green-600";
    if (grade.startsWith('B')) return "text-blue-600";
    if (grade.startsWith('C')) return "text-yellow-600";
    if (grade.startsWith('D')) return "text-orange-600";
    return "text-red-600";
  };

  const getGradeBackground = (grade: string) => {
    if (grade.startsWith('A')) return "bg-green-100";
    if (grade.startsWith('B')) return "bg-blue-100";
    if (grade.startsWith('C')) return "bg-yellow-100";
    if (grade.startsWith('D')) return "bg-orange-100";
    return "bg-red-100";
  };

  const updateGrade = (studentId: number, category: string, value: number) => {
    setGradeData(prev => 
      prev.map(student => {
        if (student.id === studentId) {
          const updatedGrades = { ...student.grades, [category]: value };
          const validGrades = Object.values(updatedGrades).filter(g => g !== null) as number[];
          const average = validGrades.length > 0 ? validGrades.reduce((a, b) => a + b, 0) / validGrades.length : 0;
          
          let letterGrade = "F";
          if (average >= 97) letterGrade = "A+";
          else if (average >= 93) letterGrade = "A";
          else if (average >= 90) letterGrade = "A-";
          else if (average >= 87) letterGrade = "B+";
          else if (average >= 83) letterGrade = "B";
          else if (average >= 80) letterGrade = "B-";
          else if (average >= 77) letterGrade = "C+";
          else if (average >= 73) letterGrade = "C";
          else if (average >= 70) letterGrade = "C-";
          else if (average >= 67) letterGrade = "D+";
          else if (average >= 65) letterGrade = "D";

          return {
            ...student,
            grades: updatedGrades,
            average: Math.round(average * 10) / 10,
            letterGrade
          };
        }
        return student;
      })
    );
  };

  const filteredStudents = gradeData.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.studentId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const classAverage = gradeData.reduce((sum, student) => sum + student.average, 0) / gradeData.length;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Grade Management</h1>
              <p className="text-gray-600">Enter and manage student grades</p>
            </div>
            <div className="flex gap-2">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Assignment
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Add New Assignment</DialogTitle>
                    <DialogDescription>
                      Create a new graded assignment or exam
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="assignmentName">Assignment Name</Label>
                      <Input id="assignmentName" placeholder="e.g., Quiz 3" />
                    </div>
                    <div>
                      <Label htmlFor="assignmentType">Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="assignment">Assignment</SelectItem>
                          <SelectItem value="quiz">Quiz</SelectItem>
                          <SelectItem value="exam">Exam</SelectItem>
                          <SelectItem value="project">Project</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="maxPoints">Maximum Points</Label>
                      <Input id="maxPoints" type="number" placeholder="100" />
                    </div>
                    <div>
                      <Label htmlFor="weight">Weight (%)</Label>
                      <Input id="weight" type="number" placeholder="15" />
                    </div>
                    <Button className="w-full">Create Assignment</Button>
                  </div>
                </DialogContent>
              </Dialog>
              <Button>
                <Save className="w-4 h-4 mr-2" />
                Save All Grades
              </Button>
            </div>
          </div>
        </div>

        {/* Controls */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Select Class</label>
                <Select value={selectedClass} onValueChange={setSelectedClass}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name} ({cls.students} students)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Select Subject</label>
                <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name} - {subject.teacher}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Search Students</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search by name or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Class Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Class Average</p>
                  <p className="text-2xl font-bold">{classAverage.toFixed(1)}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">{gradeData.length}</p>
                </div>
                <Users className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">A Grades</p>
                  <p className="text-2xl font-bold text-green-600">
                    {gradeData.filter(s => s.letterGrade.startsWith('A')).length}
                  </p>
                </div>
                <Award className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Assignments</p>
                  <p className="text-2xl font-bold">{gradeCategories.length}</p>
                </div>
                <BookOpen className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="gradebook" className="space-y-6">
          <TabsList>
            <TabsTrigger value="gradebook">Gradebook</TabsTrigger>
            <TabsTrigger value="categories">Grade Categories</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="gradebook" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Student Gradebook - {selectedClass}</CardTitle>
                <CardDescription>
                  {subjects.find(s => s.id === selectedSubject)?.name} - {subjects.find(s => s.id === selectedSubject)?.teacher}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">Student</th>
                        <th className="text-center p-3 font-medium">Assignment 1</th>
                        <th className="text-center p-3 font-medium">Assignment 2</th>
                        <th className="text-center p-3 font-medium">Quiz 1</th>
                        <th className="text-center p-3 font-medium">Quiz 2</th>
                        <th className="text-center p-3 font-medium">Midterm</th>
                        <th className="text-center p-3 font-medium">Final</th>
                        <th className="text-center p-3 font-medium">Average</th>
                        <th className="text-center p-3 font-medium">Grade</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredStudents.map((student) => (
                        <tr key={student.id} className="border-b hover:bg-gray-50">
                          <td className="p-3">
                            <div>
                              <p className="font-medium">{student.name}</p>
                              <p className="text-sm text-gray-600">{student.studentId}</p>
                            </div>
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.assignment1 || ""}
                              onChange={(e) => updateGrade(student.id, "assignment1", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.assignment2 || ""}
                              onChange={(e) => updateGrade(student.id, "assignment2", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.quiz1 || ""}
                              onChange={(e) => updateGrade(student.id, "quiz1", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.quiz2 || ""}
                              onChange={(e) => updateGrade(student.id, "quiz2", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.midterm || ""}
                              onChange={(e) => updateGrade(student.id, "midterm", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={student.grades.final || ""}
                              onChange={(e) => updateGrade(student.id, "final", parseInt(e.target.value) || 0)}
                              className="w-16 text-center"
                              placeholder="--"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <span className="font-medium">{student.average.toFixed(1)}%</span>
                          </td>
                          <td className="p-3 text-center">
                            <Badge className={`${getGradeBackground(student.letterGrade)} ${getGradeColor(student.letterGrade)}`}>
                              {student.letterGrade}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Grade Categories</CardTitle>
                <CardDescription>Manage assignment types and their weights</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {gradeCategories.map((category, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{category.name}</h4>
                          <p className="text-sm text-gray-600">
                            {category.type.charAt(0).toUpperCase() + category.type.slice(1)} • Max: {category.maxPoints} points
                          </p>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <p className="font-medium">{category.weight}%</p>
                            <p className="text-sm text-gray-600">Weight</p>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Edit className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Grade Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['A', 'B', 'C', 'D', 'F'].map((grade) => {
                      const count = gradeData.filter(s => s.letterGrade.startsWith(grade)).length;
                      const percentage = (count / gradeData.length) * 100;
                      return (
                        <div key={grade} className="flex items-center justify-between">
                          <span className="font-medium">{grade} Grades</span>
                          <div className="flex items-center gap-2">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${getGradeBackground(grade)}`}
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium w-12">{count} ({percentage.toFixed(0)}%)</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Class Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Highest Score</span>
                      <span className="font-bold text-green-600">
                        {Math.max(...gradeData.map(s => s.average)).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Lowest Score</span>
                      <span className="font-bold text-red-600">
                        {Math.min(...gradeData.map(s => s.average)).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Class Average</span>
                      <span className="font-bold text-blue-600">
                        {classAverage.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Passing Rate</span>
                      <span className="font-bold text-green-600">
                        {((gradeData.filter(s => s.average >= 65).length / gradeData.length) * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
