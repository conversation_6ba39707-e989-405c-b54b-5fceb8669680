"use client";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Building2, 
  Users, 
  GraduationCap, 
  Calendar, 
  ClipboardCheck, 
  BarChart3, 
  MessageSquare, 
  DollarSign,
  ArrowLeft
} from "lucide-react";

export default function TestPages() {
  const pageCategories = [
    {
      title: "Platform Management",
      description: "Institution and cross-platform management features",
      icon: Building2,
      color: "bg-blue-500",
      pages: [
        { name: "Institution Management", path: "/test-pages/platform/institutions", description: "Manage universities, high schools, and schools" },
        { name: "University Management", path: "/test-pages/platform/university", description: "Department and university-specific features" },
        { name: "High School Management", path: "/test-pages/platform/high-school", description: "High school administration" },
        { name: "School Management", path: "/test-pages/platform/school", description: "Primary school management" }
      ]
    },
    {
      title: "User Management",
      description: "Manage different types of users and roles",
      icon: Users,
      color: "bg-green-500",
      pages: [
        { name: "Super Admin Dashboard", path: "/test-pages/users/super-admin", description: "Platform-wide administration" },
        { name: "Institution Admin", path: "/test-pages/users/institution-admin", description: "Institution-level management" },
        { name: "Faculty Management", path: "/test-pages/users/faculty", description: "Faculty and staff management" },
        { name: "Student Management", path: "/test-pages/users/students", description: "Student information and enrollment" },
        { name: "Parent Portal", path: "/test-pages/users/parents", description: "Parent access and communication" }
      ]
    },
    {
      title: "Academic Management",
      description: "Academic years, semesters, and scheduling",
      icon: GraduationCap,
      color: "bg-purple-500",
      pages: [
        { name: "Academic Year Management", path: "/test-pages/academic/years", description: "Manage academic years and terms" },
        { name: "Semester Management", path: "/test-pages/academic/semesters", description: "Semester planning and organization" },
        { name: "Course Scheduling", path: "/test-pages/academic/scheduling", description: "Class timetables and scheduling" },
        { name: "Department Management", path: "/test-pages/academic/departments", description: "Academic departments and programs" }
      ]
    },
    {
      title: "Attendance & Tracking",
      description: "Student attendance and progress tracking",
      icon: ClipboardCheck,
      color: "bg-orange-500",
      pages: [
        { name: "Attendance Management", path: "/test-pages/attendance/management", description: "Track and manage student attendance" },
        { name: "Attendance Reports", path: "/test-pages/attendance/reports", description: "Attendance analytics and reports" }
      ]
    },
    {
      title: "Grading & Reporting",
      description: "Academic performance and reporting system",
      icon: BarChart3,
      color: "bg-red-500",
      pages: [
        { name: "Grade Management", path: "/test-pages/grading/management", description: "Enter and manage student grades" },
        { name: "Report Cards", path: "/test-pages/grading/reports", description: "Generate academic reports" },
        { name: "Analytics Dashboard", path: "/test-pages/grading/analytics", description: "Academic performance analytics" }
      ]
    },
    {
      title: "Communication",
      description: "Parent-school communication portal",
      icon: MessageSquare,
      color: "bg-indigo-500",
      pages: [
        { name: "Parent Communication", path: "/test-pages/communication/parent-portal", description: "Parent-school messaging system" },
        { name: "Notifications", path: "/test-pages/communication/notifications", description: "Alerts and announcements" },
        { name: "Messages", path: "/test-pages/communication/messages", description: "Direct messaging system" }
      ]
    },
    {
      title: "Financial Management",
      description: "Tuition, payments, and financial tracking",
      icon: DollarSign,
      color: "bg-yellow-500",
      pages: [
        { name: "Tuition Management", path: "/test-pages/financial/tuition", description: "Manage tuition fees and billing" },
        { name: "Payment Processing", path: "/test-pages/financial/payments", description: "Process and track payments" },
        { name: "Financial Reports", path: "/test-pages/financial/reports", description: "Financial analytics and reporting" },
        { name: "Receipts", path: "/test-pages/financial/receipts", description: "Generate and manage receipts" }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Jangua Schools Platform - Test Pages
          </h1>
          <p className="text-xl text-gray-600">
            Explore all the UI components and pages for the school management platform
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {pageCategories.map((category, index) => {
            const IconComponent = category.icon;
            return (
              <Card key={index} className="h-fit">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${category.color} text-white`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{category.title}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.pages.map((page, pageIndex) => (
                      <div key={pageIndex} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{page.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{page.description}</p>
                          </div>
                          <Link href={page.path}>
                            <Button size="sm" variant="outline">
                              View Page
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Development Status</CardTitle>
              <CardDescription>
                These pages are UI mockups for demonstration purposes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="secondary">Frontend Only</Badge>
                <Badge variant="secondary">No Backend Integration</Badge>
                <Badge variant="secondary">Demo Data</Badge>
                <Badge variant="secondary">UI Components</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
