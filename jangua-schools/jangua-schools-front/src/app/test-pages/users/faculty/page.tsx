"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  GraduationCap,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  Users,
  Edit,
  Eye,
  UserPlus
} from "lucide-react";

export default function FacultyManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterDepartment, setFilterDepartment] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  // Mock data for faculty members
  const facultyMembers = [
    {
      id: 1,
      employeeId: "FAC001",
      firstName: "Ahmed",
      lastName: "Benali",
      email: "<EMAIL>",
      phone: "+212 6 12 34 56 78",
      department: "Mathematics",
      position: "Senior Teacher",
      subjects: ["Algebra", "Geometry", "Calculus"],
      experience: "8 years",
      qualification: "Master's in Mathematics",
      joinDate: "2016-09-01",
      status: "active",
      classes: ["10A", "11S1", "12S1"],
      workload: 18
    },
    {
      id: 2,
      employeeId: "FAC002",
      firstName: "Fatima",
      lastName: "El-Mansouri",
      email: "<EMAIL>",
      phone: "+212 6 23 45 67 89",
      department: "Science",
      position: "Department Head",
      subjects: ["Physics", "Chemistry"],
      experience: "12 years",
      qualification: "PhD in Physics",
      joinDate: "2012-09-01",
      status: "active",
      classes: ["11S1", "11S2", "12S1"],
      workload: 15
    },
    {
      id: 3,
      employeeId: "FAC003",
      firstName: "Omar",
      lastName: "Zaki",
      email: "<EMAIL>",
      phone: "+212 6 34 56 78 90",
      department: "Languages",
      position: "Teacher",
      subjects: ["Arabic Literature", "French"],
      experience: "5 years",
      qualification: "Master's in Literature",
      joinDate: "2019-09-01",
      status: "active",
      classes: ["9A", "10B", "11L1"],
      workload: 20
    },
    {
      id: 4,
      employeeId: "FAC004",
      firstName: "Aicha",
      lastName: "Benkirane",
      email: "<EMAIL>",
      phone: "+212 6 45 67 89 01",
      department: "Science",
      position: "Teacher",
      subjects: ["Biology", "Environmental Science"],
      experience: "6 years",
      qualification: "Master's in Biology",
      joinDate: "2018-09-01",
      status: "active",
      classes: ["9B", "10A", "11S2"],
      workload: 16
    },
    {
      id: 5,
      employeeId: "FAC005",
      firstName: "Hassan",
      lastName: "Alami",
      email: "<EMAIL>",
      phone: "+212 6 56 78 90 12",
      department: "Physical Education",
      position: "Teacher",
      subjects: ["Physical Education", "Sports"],
      experience: "10 years",
      qualification: "Bachelor's in Sports Science",
      joinDate: "2014-09-01",
      status: "on_leave",
      classes: ["All Grades"],
      workload: 25
    }
  ];

  const departments = ["Mathematics", "Science", "Languages", "Social Studies", "Arts", "Physical Education"];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "on_leave":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getWorkloadColor = (workload: number) => {
    if (workload >= 20) return "text-red-600";
    if (workload >= 15) return "text-yellow-600";
    return "text-green-600";
  };

  const filteredFaculty = facultyMembers.filter(faculty => {
    const matchesSearch = 
      faculty.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faculty.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faculty.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faculty.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = filterDepartment === "all" || faculty.department === filterDepartment;
    const matchesStatus = filterStatus === "all" || faculty.status === filterStatus;
    
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Faculty Management</h1>
              <p className="text-gray-600">Manage faculty members and staff</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Add Faculty
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Faculty Member</DialogTitle>
                  <DialogDescription>
                    Register a new faculty member in the system
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" placeholder="Enter first name" />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" placeholder="Enter last name" />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept} value={dept.toLowerCase()}>
                            {dept}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="position">Position</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="teacher">Teacher</SelectItem>
                        <SelectItem value="senior-teacher">Senior Teacher</SelectItem>
                        <SelectItem value="department-head">Department Head</SelectItem>
                        <SelectItem value="coordinator">Coordinator</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="w-full">Add Faculty Member</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Faculty Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Faculty</p>
                  <p className="text-2xl font-bold">{facultyMembers.length}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-green-600">
                    {facultyMembers.filter(f => f.status === "active").length}
                  </p>
                </div>
                <Users className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">On Leave</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {facultyMembers.filter(f => f.status === "on_leave").length}
                  </p>
                </div>
                <Calendar className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Departments</p>
                  <p className="text-2xl font-bold">{departments.length}</p>
                </div>
                <BookOpen className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search faculty by name, ID, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="on_leave">On Leave</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Faculty Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredFaculty.map((faculty) => (
            <Card key={faculty.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${faculty.firstName}`} />
                      <AvatarFallback>
                        {faculty.firstName[0]}{faculty.lastName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">
                        {faculty.firstName} {faculty.lastName}
                      </CardTitle>
                      <CardDescription>
                        {faculty.position} • ID: {faculty.employeeId}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(faculty.status)}>
                    {faculty.status.replace("_", " ")}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Department:</span>
                      <p className="font-medium">{faculty.department}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Experience:</span>
                      <p className="font-medium">{faculty.experience}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Workload:</span>
                      <p className={`font-medium ${getWorkloadColor(faculty.workload)}`}>
                        {faculty.workload} hrs/week
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">Classes:</span>
                      <p className="font-medium">{faculty.classes.length}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span className="truncate">{faculty.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Phone className="w-4 h-4" />
                      <span>{faculty.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>Joined: {faculty.joinDate}</span>
                    </div>
                  </div>

                  <div>
                    <span className="text-gray-500 text-sm">Subjects:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {faculty.subjects.map((subject, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <span className="text-gray-500 text-sm">Classes:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {faculty.classes.map((cls, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {cls}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredFaculty.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No faculty members found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
