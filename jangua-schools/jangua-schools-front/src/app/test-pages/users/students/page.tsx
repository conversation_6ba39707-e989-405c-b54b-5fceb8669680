"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Filter,
  GraduationCap,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Edit,
  Eye,
  UserPlus
} from "lucide-react";

export default function StudentManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterGrade, setFilterGrade] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  // Mock data for students
  const students = [
    {
      id: 1,
      studentId: "STU001",
      firstName: "Ahmed",
      lastName: "Benali",
      email: "<EMAIL>",
      phone: "+212 6 12 34 56 78",
      grade: "Grade 10",
      section: "10A",
      dateOfBirth: "2008-05-15",
      address: "123 Hassan II Ave, Casablanca",
      parentName: "Omar Benali",
      parentPhone: "+212 6 87 65 43 21",
      enrollmentDate: "2023-09-01",
      status: "active",
      gpa: 3.8,
      attendance: 95
    },
    {
      id: 2,
      studentId: "STU002",
      firstName: "Fatima",
      lastName: "El-Mansouri",
      email: "<EMAIL>",
      phone: "+212 6 23 45 67 89",
      grade: "Grade 11",
      section: "11S1",
      dateOfBirth: "2007-08-22",
      address: "456 Mohammed V St, Rabat",
      parentName: "Aicha El-Mansouri",
      parentPhone: "+212 6 98 76 54 32",
      enrollmentDate: "2022-09-01",
      status: "active",
      gpa: 4.0,
      attendance: 98
    },
    {
      id: 3,
      studentId: "STU003",
      firstName: "Youssef",
      lastName: "Alami",
      email: "<EMAIL>",
      phone: "+212 6 34 56 78 90",
      grade: "Grade 9",
      section: "9B",
      dateOfBirth: "2009-12-10",
      address: "789 Atlas Blvd, Marrakech",
      parentName: "Hassan Alami",
      parentPhone: "+212 6 09 87 65 43",
      enrollmentDate: "2024-09-01",
      status: "active",
      gpa: 3.5,
      attendance: 92
    },
    {
      id: 4,
      studentId: "STU004",
      firstName: "Khadija",
      lastName: "Benkirane",
      email: "<EMAIL>",
      phone: "+212 6 45 67 89 01",
      grade: "Grade 12",
      section: "12S2",
      dateOfBirth: "2006-03-18",
      address: "321 Corniche Rd, Tangier",
      parentName: "Meryem Benkirane",
      parentPhone: "+212 6 10 98 76 54",
      enrollmentDate: "2021-09-01",
      status: "active",
      gpa: 3.9,
      attendance: 96
    },
    {
      id: 5,
      studentId: "STU005",
      firstName: "Omar",
      lastName: "Zaki",
      email: "<EMAIL>",
      phone: "+212 6 56 78 90 12",
      grade: "Grade 10",
      section: "10C",
      dateOfBirth: "2008-07-25",
      address: "654 Agdal District, Rabat",
      parentName: "Rachid Zaki",
      parentPhone: "+212 6 21 09 87 65",
      enrollmentDate: "2023-09-01",
      status: "inactive",
      gpa: 3.2,
      attendance: 78
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      case "suspended":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getGPAColor = (gpa: number) => {
    if (gpa >= 3.7) return "text-green-600";
    if (gpa >= 3.0) return "text-yellow-600";
    return "text-red-600";
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = 
      student.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesGrade = filterGrade === "all" || student.grade === filterGrade;
    const matchesStatus = filterStatus === "all" || student.status === filterStatus;
    
    return matchesSearch && matchesGrade && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Student Management</h1>
              <p className="text-gray-600">Manage student information and enrollment</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Add Student
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Student</DialogTitle>
                  <DialogDescription>
                    Register a new student in the system
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" placeholder="Enter first name" />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" placeholder="Enter last name" />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="grade">Grade</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select grade" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grade-9">Grade 9</SelectItem>
                        <SelectItem value="grade-10">Grade 10</SelectItem>
                        <SelectItem value="grade-11">Grade 11</SelectItem>
                        <SelectItem value="grade-12">Grade 12</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="parentName">Parent/Guardian Name</Label>
                    <Input id="parentName" placeholder="Enter parent name" />
                  </div>
                  <Button className="w-full">Register Student</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search students by name, ID, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterGrade} onValueChange={setFilterGrade}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  <SelectItem value="Grade 9">Grade 9</SelectItem>
                  <SelectItem value="Grade 10">Grade 10</SelectItem>
                  <SelectItem value="Grade 11">Grade 11</SelectItem>
                  <SelectItem value="Grade 12">Grade 12</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Students Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredStudents.map((student) => (
            <Card key={student.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${student.firstName}`} />
                      <AvatarFallback>
                        {student.firstName[0]}{student.lastName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">
                        {student.firstName} {student.lastName}
                      </CardTitle>
                      <CardDescription>ID: {student.studentId}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(student.status)}>
                    {student.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Grade:</span>
                      <p className="font-medium">{student.grade}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Section:</span>
                      <p className="font-medium">{student.section}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">GPA:</span>
                      <p className={`font-medium ${getGPAColor(student.gpa)}`}>
                        {student.gpa.toFixed(1)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">Attendance:</span>
                      <p className="font-medium">{student.attendance}%</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span className="truncate">{student.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Phone className="w-4 h-4" />
                      <span>{student.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span className="truncate">{student.address}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>Born: {student.dateOfBirth}</span>
                    </div>
                  </div>

                  <div className="pt-3 border-t">
                    <p className="text-sm text-gray-500 mb-1">Parent/Guardian:</p>
                    <p className="text-sm font-medium">{student.parentName}</p>
                    <p className="text-sm text-gray-600">{student.parentPhone}</p>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredStudents.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No students found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
