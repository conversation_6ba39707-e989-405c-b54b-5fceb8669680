"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Users, 
  GraduationCap, 
  Building2, 
  TrendingUp,
  Calendar,
  Bell,
  Settings,
  UserPlus,
  BookOpen,
  ClipboardCheck,
  DollarSign
} from "lucide-react";

export default function InstitutionAdminDashboard() {
  // Mock data for institution
  const institution = {
    name: "Al-Andalus High School",
    type: "High School",
    location: "Rabat, Morocco",
    established: "1995",
    totalStudents: 1200,
    totalFaculty: 85,
    totalClasses: 36,
    currentSemester: "Spring 2024"
  };

  // Mock data for quick stats
  const quickStats = {
    studentsPresent: 1145,
    facultyPresent: 82,
    classesInSession: 28,
    pendingApplications: 15,
    upcomingEvents: 3,
    overduePayments: 8
  };

  // Mock data for recent activities
  const recentActivities = [
    { id: 1, type: "enrollment", description: "New student Ahmed Hassan enrolled in Grade 10", time: "2 hours ago" },
    { id: 2, type: "faculty", description: "Dr. Fatima Alami joined Mathematics department", time: "1 day ago" },
    { id: 3, type: "grade", description: "Grade 12 final exam results published", time: "2 days ago" },
    { id: 4, type: "payment", description: "Monthly tuition collection completed", time: "3 days ago" },
    { id: 5, type: "event", description: "Parent-teacher conference scheduled", time: "1 week ago" }
  ];

  // Mock data for departments
  const departments = [
    { name: "Mathematics", head: "Dr. Ahmed Benali", students: 1200, faculty: 12, performance: 88 },
    { name: "Science", head: "Dr. Fatima El-Mansouri", students: 1200, faculty: 15, performance: 92 },
    { name: "Languages", head: "Ms. Aicha Benkirane", students: 1200, faculty: 18, performance: 85 },
    { name: "Social Studies", head: "Mr. Omar Zaki", students: 1200, faculty: 10, performance: 87 },
    { name: "Arts", head: "Ms. Sarah Johnson", students: 800, faculty: 8, performance: 90 },
    { name: "Physical Education", head: "Mr. Hassan Alami", students: 1200, faculty: 6, performance: 95 }
  ];

  // Mock data for upcoming events
  const upcomingEvents = [
    { id: 1, title: "Parent-Teacher Conference", date: "2024-02-15", type: "meeting", attendees: 150 },
    { id: 2, title: "Science Fair", date: "2024-02-20", type: "academic", attendees: 300 },
    { id: 3, title: "Sports Day", date: "2024-02-25", type: "sports", attendees: 1200 }
  ];

  // Mock data for notifications
  const notifications = [
    { id: 1, title: "System Maintenance", message: "Scheduled maintenance tonight 11 PM - 2 AM", urgent: true, time: "1 hour ago" },
    { id: 2, title: "New Feature", message: "Parent portal messaging system is now live", urgent: false, time: "2 days ago" },
    { id: 3, title: "Payment Reminder", message: "8 students have overdue payments", urgent: true, time: "3 days ago" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Institution Admin Dashboard</h1>
              <p className="text-gray-600">{institution.name} - {institution.currentSemester}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Add User
              </Button>
            </div>
          </div>
        </div>

        {/* Institution Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">{institution.totalStudents.toLocaleString()}</p>
                  <p className="text-xs text-green-600">Present: {quickStats.studentsPresent}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div className="mt-4">
                <Progress value={(quickStats.studentsPresent / institution.totalStudents) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Faculty Members</p>
                  <p className="text-2xl font-bold">{institution.totalFaculty}</p>
                  <p className="text-xs text-green-600">Present: {quickStats.facultyPresent}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-green-600" />
              </div>
              <div className="mt-4">
                <Progress value={(quickStats.facultyPresent / institution.totalFaculty) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Classes</p>
                  <p className="text-2xl font-bold">{quickStats.classesInSession}</p>
                  <p className="text-xs text-gray-600">of {institution.totalClasses} total</p>
                </div>
                <Building2 className="w-8 h-8 text-purple-600" />
              </div>
              <div className="mt-4">
                <Progress value={(quickStats.classesInSession / institution.totalClasses) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Items</p>
                  <p className="text-2xl font-bold">{quickStats.pendingApplications + quickStats.overduePayments}</p>
                  <p className="text-xs text-yellow-600">Needs attention</p>
                </div>
                <Bell className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="departments">Departments</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Activities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Recent Activities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivities.map((activity) => (
                      <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.description}</p>
                          <p className="text-xs text-gray-500">{activity.time}</p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {activity.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="h-20 flex-col">
                      <Users className="w-6 h-6 mb-2" />
                      <span className="text-sm">Manage Students</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <GraduationCap className="w-6 h-6 mb-2" />
                      <span className="text-sm">Manage Faculty</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <BookOpen className="w-6 h-6 mb-2" />
                      <span className="text-sm">Academic Records</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <ClipboardCheck className="w-6 h-6 mb-2" />
                      <span className="text-sm">Attendance</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <DollarSign className="w-6 h-6 mb-2" />
                      <span className="text-sm">Financial</span>
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <Calendar className="w-6 h-6 mb-2" />
                      <span className="text-sm">Schedule</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="departments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Department Overview</CardTitle>
                <CardDescription>Performance and statistics by department</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {departments.map((dept, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{dept.name}</h4>
                          <p className="text-sm text-gray-600">Head: {dept.head}</p>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800">
                          {dept.performance}% Performance
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Students:</span>
                          <p className="font-medium">{dept.students}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Faculty:</span>
                          <p className="font-medium">{dept.faculty}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Performance:</span>
                          <Progress value={dept.performance} className="h-2 mt-1" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-gray-600">{event.date}</p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">{event.type}</Badge>
                          <p className="text-sm text-gray-600 mt-1">{event.attendees} attendees</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  System Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{notification.title}</h4>
                            {notification.urgent && (
                              <Badge variant="destructive" className="text-xs">Urgent</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{notification.message}</p>
                          <p className="text-xs text-gray-500 mt-2">{notification.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
