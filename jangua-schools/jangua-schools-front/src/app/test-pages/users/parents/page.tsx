"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Bell,
  Calendar,
  GraduationCap,
  DollarSign,
  MessageSquare,
  ClipboardCheck,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  Star
} from "lucide-react";

export default function ParentPortal() {
  // Mock data for parent and children
  const parentInfo = {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+212 6 87 65 43 21"
  };

  const children = [
    {
      id: 1,
      name: "<PERSON>",
      grade: "Grade 10",
      section: "10A",
      photo: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ahmed",
      gpa: 3.8,
      attendance: 95,
      nextClass: "Mathematics at 9:00 AM"
    },
    {
      id: 2,
      name: "Fatima Benali",
      grade: "Grade 7",
      section: "7B",
      photo: "https://api.dicebear.com/7.x/avataaars/svg?seed=Fatima",
      gpa: 4.0,
      attendance: 98,
      nextClass: "Science at 10:30 AM"
    }
  ];

  // Mock data for recent grades
  const recentGrades = [
    { id: 1, student: "Ahmed Benali", subject: "Mathematics", grade: "A-", date: "2024-01-18" },
    { id: 2, student: "Fatima Benali", subject: "Science", grade: "A+", date: "2024-01-17" },
    { id: 3, student: "Ahmed Benali", subject: "Physics", grade: "B+", date: "2024-01-15" },
    { id: 4, student: "Fatima Benali", subject: "English", grade: "A", date: "2024-01-14" }
  ];

  // Mock data for attendance
  const attendanceData = [
    { id: 1, student: "Ahmed Benali", date: "2024-01-20", status: "present", subject: "All Classes" },
    { id: 2, student: "Fatima Benali", date: "2024-01-20", status: "present", subject: "All Classes" },
    { id: 3, student: "Ahmed Benali", date: "2024-01-19", status: "absent", subject: "PE", reason: "Sick" },
    { id: 4, student: "Fatima Benali", date: "2024-01-19", status: "present", subject: "All Classes" }
  ];

  // Mock data for notifications
  const notifications = [
    { id: 1, type: "payment", title: "Tuition Payment Due", message: "Monthly tuition payment due on Jan 25", time: "2 hours ago", urgent: true },
    { id: 2, type: "grade", title: "New Grade Posted", message: "Ahmed received A- in Mathematics", time: "1 day ago", urgent: false },
    { id: 3, type: "event", title: "Parent-Teacher Conference", message: "Scheduled for Feb 15 at 2:00 PM", time: "2 days ago", urgent: false },
    { id: 4, type: "attendance", title: "Attendance Alert", message: "Ahmed was absent from PE class", time: "3 days ago", urgent: true }
  ];

  // Mock data for upcoming payments
  const upcomingPayments = [
    { id: 1, description: "Monthly Tuition - February", amount: 1200, dueDate: "2024-02-01", status: "pending" },
    { id: 2, description: "School Trip Fee", amount: 150, dueDate: "2024-02-15", status: "pending" },
    { id: 3, description: "Book Fee", amount: 80, dueDate: "2024-02-20", status: "pending" }
  ];

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return "text-green-600";
    if (grade.startsWith('B')) return "text-blue-600";
    if (grade.startsWith('C')) return "text-yellow-600";
    return "text-red-600";
  };

  const getAttendanceIcon = (status: string) => {
    return status === "present" ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <AlertCircle className="w-4 h-4 text-red-500" />;
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "payment":
        return <DollarSign className="w-4 h-4 text-yellow-500" />;
      case "grade":
        return <Star className="w-4 h-4 text-blue-500" />;
      case "event":
        return <Calendar className="w-4 h-4 text-purple-500" />;
      case "attendance":
        return <ClipboardCheck className="w-4 h-4 text-red-500" />;
      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Parent Portal</h1>
              <p className="text-gray-600">Welcome back, {parentInfo.name}</p>
            </div>
            <Button>
              <MessageSquare className="w-4 h-4 mr-2" />
              Contact School
            </Button>
          </div>
        </div>

        {/* Children Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {children.map((child) => (
            <Card key={child.id}>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={child.photo} />
                    <AvatarFallback>{child.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-xl">{child.name}</CardTitle>
                    <CardDescription>{child.grade} - {child.section}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">GPA</span>
                      <p className="text-lg font-bold text-green-600">{child.gpa}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Attendance</span>
                      <p className="text-lg font-bold text-blue-600">{child.attendance}%</p>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Next Class</span>
                    <p className="font-medium">{child.nextClass}</p>
                  </div>
                  <Progress value={child.attendance} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="grades">Grades</TabsTrigger>
            <TabsTrigger value="attendance">Attendance</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Notifications */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="w-5 h-5" />
                    Recent Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {notifications.slice(0, 4).map((notification) => (
                      <div key={notification.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium">{notification.title}</p>
                            {notification.urgent && (
                              <Badge variant="destructive" className="text-xs">Urgent</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{notification.message}</p>
                          <p className="text-xs text-gray-500">{notification.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Quick Stats
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Overall GPA</span>
                      <span className="font-bold text-green-600">3.9</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Average Attendance</span>
                      <span className="font-bold text-blue-600">96.5%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Pending Payments</span>
                      <span className="font-bold text-yellow-600">$1,430</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Unread Messages</span>
                      <span className="font-bold text-purple-600">3</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="grades" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Grades</CardTitle>
                <CardDescription>Latest academic performance updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentGrades.map((grade) => (
                    <div key={grade.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{grade.student}</p>
                        <p className="text-sm text-gray-600">{grade.subject}</p>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${getGradeColor(grade.grade)}`}>
                          {grade.grade}
                        </p>
                        <p className="text-xs text-gray-500">{grade.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="attendance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Attendance Records</CardTitle>
                <CardDescription>Recent attendance information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {attendanceData.map((record) => (
                    <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getAttendanceIcon(record.status)}
                        <div>
                          <p className="font-medium">{record.student}</p>
                          <p className="text-sm text-gray-600">{record.subject}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{record.date}</p>
                        <p className="text-xs text-gray-500">
                          {record.status === "absent" && record.reason ? record.reason : record.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Payments</CardTitle>
                <CardDescription>Pending fees and tuition payments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {upcomingPayments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{payment.description}</p>
                        <p className="text-sm text-gray-600">Due: {payment.dueDate}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">${payment.amount}</p>
                        <Button size="sm">Pay Now</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="messages" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Messages</CardTitle>
                <CardDescription>Communication with school staff</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                  <p className="text-gray-600 mb-4">Start a conversation with your child's teachers</p>
                  <Button>Send Message</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
