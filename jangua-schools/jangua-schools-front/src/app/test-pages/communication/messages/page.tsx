"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  MessageSquare,
  Send,
  Reply,
  Forward,
  Archive,
  Trash2,
  Star,
  Clock,
  CheckCircle
} from "lucide-react";

export default function MessagesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<number | null>(null);
  const [filterType, setFilterType] = useState("all");

  // Mock data for messages
  const messages = [
    {
      id: 1,
      from: "Dr. Ahmed Benali",
      fromRole: "Teacher",
      to: "Parent Portal",
      subject: "Ahmed's Progress Update",
      preview: "I wanted to update you on Ahmed's excellent progress in Mathematics this semester...",
      content: "Dear Mr. and Mrs. Benali,\n\nI wanted to update you on Ahmed's excellent progress in Mathematics this semester. He has shown remarkable improvement in his problem-solving skills and consistently participates in class discussions.\n\nHis recent test scores have been:\n- Quiz 1: 85%\n- Quiz 2: 92%\n- Midterm: 88%\n\nI encourage him to continue this positive trend. Please let me know if you have any questions.\n\nBest regards,\nDr. Ahmed Benali\nMathematics Department",
      timestamp: "2024-01-20 14:30",
      read: false,
      starred: true,
      type: "academic",
      priority: "normal"
    },
    {
      id: 2,
      from: "School Administration",
      fromRole: "Admin",
      to: "All Parents",
      subject: "Parent-Teacher Conference Schedule",
      preview: "We are pleased to announce the upcoming Parent-Teacher Conference scheduled for February 15th...",
      content: "Dear Parents,\n\nWe are pleased to announce the upcoming Parent-Teacher Conference scheduled for February 15th, 2024.\n\nSchedule:\n- Time: 2:00 PM - 6:00 PM\n- Location: School Main Hall\n- Duration: 15 minutes per meeting\n\nPlease book your slot through the parent portal by February 10th.\n\nThank you,\nSchool Administration",
      timestamp: "2024-01-19 09:15",
      read: true,
      starred: false,
      type: "announcement",
      priority: "high"
    },
    {
      id: 3,
      from: "Ms. Fatima El-Mansouri",
      fromRole: "Teacher",
      to: "Grade 11 Parents",
      subject: "Science Fair Project Guidelines",
      preview: "The annual Science Fair is approaching and I wanted to share the project guidelines...",
      content: "Dear Parents,\n\nThe annual Science Fair is approaching and I wanted to share the project guidelines with you.\n\nKey Dates:\n- Project proposal due: March 1st\n- Final submission: March 20th\n- Presentation day: March 25th\n\nPlease encourage your children to start early and choose topics that interest them.\n\nBest regards,\nMs. Fatima El-Mansouri\nScience Department",
      timestamp: "2024-01-18 16:45",
      read: true,
      starred: false,
      type: "academic",
      priority: "normal"
    },
    {
      id: 4,
      from: "Finance Department",
      fromRole: "Admin",
      to: "Parents",
      subject: "February Tuition Payment Reminder",
      preview: "This is a friendly reminder that February tuition payment is due on February 1st...",
      content: "Dear Parents,\n\nThis is a friendly reminder that February tuition payment is due on February 1st, 2024.\n\nPayment Details:\n- Amount: $1,200\n- Due Date: February 1st, 2024\n- Late fee: $50 after February 5th\n\nYou can make payments through:\n- Online portal\n- Bank transfer\n- Cash at school office\n\nThank you,\nFinance Department",
      timestamp: "2024-01-17 11:20",
      read: false,
      starred: false,
      type: "financial",
      priority: "high"
    },
    {
      id: 5,
      from: "Mr. Omar Zaki",
      fromRole: "Teacher",
      to: "Grade 10 Parents",
      subject: "Literature Assignment Extension",
      preview: "Due to the recent holiday, I am extending the deadline for the literature essay assignment...",
      content: "Dear Parents,\n\nDue to the recent holiday, I am extending the deadline for the literature essay assignment from January 25th to January 30th.\n\nThis will give students additional time to complete their analysis of the assigned novel.\n\nPlease remind your children about this extension.\n\nBest regards,\nMr. Omar Zaki\nLiterature Department",
      timestamp: "2024-01-16 13:10",
      read: true,
      starred: false,
      type: "academic",
      priority: "normal"
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case "academic":
        return "bg-blue-100 text-blue-800";
      case "announcement":
        return "bg-purple-100 text-purple-800";
      case "financial":
        return "bg-yellow-100 text-yellow-800";
      case "event":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600";
      case "normal":
        return "text-gray-600";
      case "low":
        return "text-gray-400";
      default:
        return "text-gray-600";
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = 
      message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.preview.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === "all" || message.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const selectedMessageData = selectedMessage ? messages.find(m => m.id === selectedMessage) : null;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
              <p className="text-gray-600">Direct messaging system for school communication</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Compose Message
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Compose New Message</DialogTitle>
                  <DialogDescription>
                    Send a message to teachers, parents, or administrators
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="recipient">Recipient</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select recipient" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="teacher">All Teachers</SelectItem>
                        <SelectItem value="parents">All Parents</SelectItem>
                        <SelectItem value="grade10">Grade 10 Parents</SelectItem>
                        <SelectItem value="grade11">Grade 11 Parents</SelectItem>
                        <SelectItem value="admin">Administration</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="subject">Subject</Label>
                    <Input id="subject" placeholder="Enter message subject" />
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea 
                      id="message" 
                      placeholder="Type your message here..." 
                      rows={6}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Send className="w-4 h-4 mr-2" />
                      Send Message
                    </Button>
                    <Button variant="outline">Save Draft</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Messages List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Inbox
                </CardTitle>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="academic">Academic</SelectItem>
                      <SelectItem value="announcement">Announcements</SelectItem>
                      <SelectItem value="financial">Financial</SelectItem>
                      <SelectItem value="event">Events</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {filteredMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedMessage === message.id ? "bg-blue-50 border-blue-200" : ""
                      } ${!message.read ? "bg-blue-25" : ""}`}
                      onClick={() => setSelectedMessage(message.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${message.from}`} />
                          <AvatarFallback>
                            {message.from.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className={`text-sm font-medium truncate ${!message.read ? "font-bold" : ""}`}>
                              {message.from}
                            </p>
                            {message.starred && <Star className="w-3 h-3 text-yellow-500 fill-current" />}
                            {!message.read && <div className="w-2 h-2 bg-blue-600 rounded-full"></div>}
                          </div>
                          <p className={`text-sm truncate ${!message.read ? "font-semibold" : "text-gray-600"}`}>
                            {message.subject}
                          </p>
                          <p className="text-xs text-gray-500 truncate mt-1">
                            {message.preview}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge className={getTypeColor(message.type)} variant="outline">
                              {message.type}
                            </Badge>
                            <span className="text-xs text-gray-500">{message.timestamp}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Message Detail */}
          <div className="lg:col-span-2">
            {selectedMessageData ? (
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${selectedMessageData.from}`} />
                        <AvatarFallback>
                          {selectedMessageData.from.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-xl">{selectedMessageData.subject}</CardTitle>
                        <CardDescription>
                          From: {selectedMessageData.from} ({selectedMessageData.fromRole}) • {selectedMessageData.timestamp}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Star className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Archive className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={getTypeColor(selectedMessageData.type)}>
                      {selectedMessageData.type}
                    </Badge>
                    <Badge variant="outline" className={getPriorityColor(selectedMessageData.priority)}>
                      {selectedMessageData.priority} priority
                    </Badge>
                    {!selectedMessageData.read && (
                      <Badge className="bg-blue-100 text-blue-800">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Unread
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap text-gray-700">
                        {selectedMessageData.content}
                      </div>
                    </div>
                    
                    <div className="flex gap-2 pt-4 border-t">
                      <Button>
                        <Reply className="w-4 h-4 mr-2" />
                        Reply
                      </Button>
                      <Button variant="outline">
                        <Forward className="w-4 h-4 mr-2" />
                        Forward
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="h-96 flex items-center justify-center">
                <div className="text-center">
                  <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a message</h3>
                  <p className="text-gray-600">Choose a message from the inbox to view its content</p>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Message Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Messages</p>
                  <p className="text-2xl font-bold">{messages.length}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unread</p>
                  <p className="text-2xl font-bold text-red-600">
                    {messages.filter(m => !m.read).length}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Starred</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {messages.filter(m => m.starred).length}
                  </p>
                </div>
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">High Priority</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {messages.filter(m => m.priority === "high").length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
