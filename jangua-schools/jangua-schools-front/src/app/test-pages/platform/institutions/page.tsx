"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Building2, 
  GraduationCap, 
  School, 
  Users, 
  MapPin,
  Edit,
  Trash2,
  Eye
} from "lucide-react";

export default function InstitutionManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  // Mock data for institutions
  const institutions = [
    {
      id: 1,
      name: "University of Technology",
      type: "university",
      location: "Casablanca, Morocco",
      students: 15000,
      faculty: 800,
      status: "active",
      established: "1985",
      departments: 12
    },
    {
      id: 2,
      name: "Al-Andalus High School",
      type: "high-school",
      location: "Rabat, Morocco",
      students: 1200,
      faculty: 85,
      status: "active",
      established: "1995",
      departments: 6
    },
    {
      id: 3,
      name: "Green Valley Elementary",
      type: "school",
      location: "Marrakech, Morocco",
      students: 450,
      faculty: 25,
      status: "active",
      established: "2010",
      departments: 3
    },
    {
      id: 4,
      name: "International Business School",
      type: "university",
      location: "Tangier, Morocco",
      students: 8500,
      faculty: 420,
      status: "pending",
      established: "2020",
      departments: 8
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "university":
        return <GraduationCap className="w-5 h-5" />;
      case "high-school":
        return <School className="w-5 h-5" />;
      case "school":
        return <Building2 className="w-5 h-5" />;
      default:
        return <Building2 className="w-5 h-5" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "university":
        return "bg-purple-100 text-purple-800";
      case "high-school":
        return "bg-blue-100 text-blue-800";
      case "school":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredInstitutions = institutions.filter(institution => {
    const matchesSearch = institution.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         institution.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || institution.type === filterType;
    return matchesSearch && matchesType;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Institution Management</h1>
              <p className="text-gray-600">Manage universities, high schools, and elementary schools</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Institution
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Institution</DialogTitle>
                  <DialogDescription>
                    Create a new educational institution in the platform
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Institution Name</Label>
                    <Input id="name" placeholder="Enter institution name" />
                  </div>
                  <div>
                    <Label htmlFor="type">Institution Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="university">University</SelectItem>
                        <SelectItem value="high-school">High School</SelectItem>
                        <SelectItem value="school">Elementary School</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input id="location" placeholder="City, Country" />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" placeholder="Brief description of the institution" />
                  </div>
                  <Button className="w-full">Create Institution</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search institutions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="university">Universities</SelectItem>
                  <SelectItem value="high-school">High Schools</SelectItem>
                  <SelectItem value="school">Elementary Schools</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Institutions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredInstitutions.map((institution) => (
            <Card key={institution.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${getTypeColor(institution.type)}`}>
                      {getTypeIcon(institution.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{institution.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getTypeColor(institution.type)}>
                          {institution.type.replace("-", " ")}
                        </Badge>
                        <Badge className={getStatusColor(institution.status)}>
                          {institution.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="w-4 h-4" />
                    {institution.location}
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Students:</span>
                      <p className="font-medium">{institution.students.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Faculty:</span>
                      <p className="font-medium">{institution.faculty}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Established:</span>
                      <p className="font-medium">{institution.established}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Departments:</span>
                      <p className="font-medium">{institution.departments}</p>
                    </div>
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                    <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredInstitutions.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No institutions found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
