"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  BookOpen, 
  School, 
  GraduationCap,
  Calendar,
  Award,
  Search,
  Clock,
  Target
} from "lucide-react";

export default function HighSchoolManagement() {
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data for high school
  const highSchool = {
    name: "Al-Andalus High School",
    location: "Rabat, Morocco",
    established: "1995",
    totalStudents: 1200,
    totalTeachers: 85,
    totalClasses: 36,
    graduationRate: 92
  };

  // Mock data for classes/grades
  const classes = [
    {
      id: 1,
      grade: "Grade 9",
      sections: ["9A", "9B", "9C", "9D"],
      students: 120,
      teachers: 8,
      subjects: ["Math", "Science", "English", "Arabic", "French", "History", "Geography", "PE"],
      classTeacher: "Ms. Amina Benali"
    },
    {
      id: 2,
      grade: "Grade 10",
      sections: ["10A", "10B", "10C", "10D"],
      students: 115,
      teachers: 8,
      subjects: ["Math", "Physics", "Chemistry", "Biology", "English", "Arabic", "French", "History"],
      classTeacher: "Mr. Hassan Alami"
    },
    {
      id: 3,
      grade: "Grade 11 - Science",
      sections: ["11S1", "11S2", "11S3"],
      students: 90,
      teachers: 10,
      subjects: ["Advanced Math", "Physics", "Chemistry", "Biology", "English", "Arabic", "Philosophy"],
      classTeacher: "Dr. Fatima El-Mansouri"
    },
    {
      id: 4,
      grade: "Grade 11 - Literature",
      sections: ["11L1", "11L2"],
      students: 60,
      teachers: 8,
      subjects: ["Arabic Literature", "French Literature", "English", "History", "Geography", "Philosophy"],
      classTeacher: "Mr. Omar Zaki"
    },
    {
      id: 5,
      grade: "Grade 12 - Science",
      sections: ["12S1", "12S2", "12S3"],
      students: 85,
      teachers: 10,
      subjects: ["Advanced Math", "Physics", "Chemistry", "Biology", "English", "Arabic", "Philosophy"],
      classTeacher: "Dr. Aicha Benkirane"
    },
    {
      id: 6,
      grade: "Grade 12 - Literature",
      sections: ["12L1", "12L2"],
      students: 55,
      teachers: 8,
      subjects: ["Arabic Literature", "French Literature", "English", "History", "Geography", "Philosophy"],
      classTeacher: "Ms. Sarah Johnson"
    }
  ];

  // Mock data for academic performance
  const academicPerformance = [
    { subject: "Mathematics", average: 78, passRate: 85 },
    { subject: "Physics", average: 75, passRate: 82 },
    { subject: "Chemistry", average: 80, passRate: 88 },
    { subject: "Biology", average: 82, passRate: 90 },
    { subject: "English", average: 76, passRate: 84 },
    { subject: "Arabic", average: 85, passRate: 92 },
    { subject: "French", average: 72, passRate: 78 }
  ];

  // Mock data for upcoming events
  const upcomingEvents = [
    { id: 1, title: "Parent-Teacher Conference", date: "2024-02-15", type: "meeting" },
    { id: 2, title: "Science Fair", date: "2024-02-20", type: "event" },
    { id: 3, title: "Mid-term Examinations", date: "2024-02-25", type: "exam" },
    { id: 4, title: "Sports Day", date: "2024-03-05", type: "event" },
    { id: 5, title: "Graduation Ceremony", date: "2024-06-15", type: "ceremony" }
  ];

  const filteredClasses = classes.filter(cls =>
    cls.grade.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cls.classTeacher.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{highSchool.name}</h1>
              <p className="text-gray-600">High School Management Dashboard</p>
            </div>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Class
            </Button>
          </div>
        </div>

        {/* High School Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">{highSchool.totalStudents.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div className="mt-4">
                <Progress value={80} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">80% capacity</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Teachers</p>
                  <p className="text-2xl font-bold">{highSchool.totalTeachers}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-green-600" />
              </div>
              <div className="mt-4">
                <Badge variant="secondary">Fully Staffed</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Classes</p>
                  <p className="text-2xl font-bold">{highSchool.totalClasses}</p>
                </div>
                <School className="w-8 h-8 text-purple-600" />
              </div>
              <div className="mt-4">
                <Badge className="bg-blue-100 text-blue-800">6 Grades</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Graduation Rate</p>
                  <p className="text-2xl font-bold">{highSchool.graduationRate}%</p>
                </div>
                <Award className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="mt-4">
                <Progress value={highSchool.graduationRate} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="classes" className="space-y-6">
          <TabsList>
            <TabsTrigger value="classes">Classes & Grades</TabsTrigger>
            <TabsTrigger value="performance">Academic Performance</TabsTrigger>
            <TabsTrigger value="events">Upcoming Events</TabsTrigger>
          </TabsList>

          <TabsContent value="classes" className="space-y-6">
            {/* Search */}
            <Card>
              <CardContent className="pt-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search classes or teachers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Classes Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredClasses.map((classItem) => (
                <Card key={classItem.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{classItem.grade}</CardTitle>
                        <CardDescription>Class Teacher: {classItem.classTeacher}</CardDescription>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Students:</span>
                          <p className="font-medium">{classItem.students}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Teachers:</span>
                          <p className="font-medium">{classItem.teachers}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Sections:</span>
                          <p className="font-medium">{classItem.sections.length}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Subjects:</span>
                          <p className="font-medium">{classItem.subjects.length}</p>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500 text-sm">Sections:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {classItem.sections.map((section, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500 text-sm">Subjects:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {classItem.subjects.slice(0, 4).map((subject, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {subject}
                            </Badge>
                          ))}
                          {classItem.subjects.length > 4 && (
                            <Badge variant="outline" className="text-xs">
                              +{classItem.subjects.length - 4} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Academic Performance by Subject
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {academicPerformance.map((subject, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{subject.subject}</span>
                        <div className="flex gap-4 text-sm">
                          <span>Avg: {subject.average}%</span>
                          <span>Pass: {subject.passRate}%</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Progress value={subject.average} className="h-2" />
                          <p className="text-xs text-gray-500 mt-1">Average Score</p>
                        </div>
                        <div>
                          <Progress value={subject.passRate} className="h-2" />
                          <p className="text-xs text-gray-500 mt-1">Pass Rate</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Clock className="w-5 h-5 text-gray-400" />
                      <div className="flex-1">
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-gray-500">{event.date}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {event.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
