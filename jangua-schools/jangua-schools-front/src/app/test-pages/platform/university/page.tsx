"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  BookOpen, 
  Building, 
  GraduationCap,
  TrendingUp,
  Calendar,
  Award,
  Search
} from "lucide-react";

export default function UniversityManagement() {
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data for university
  const university = {
    name: "University of Technology",
    location: "Casablanca, Morocco",
    established: "1985",
    totalStudents: 15000,
    totalFaculty: 800,
    totalDepartments: 12,
    accreditation: "AACSB, ABET"
  };

  // Mock data for departments
  const departments = [
    {
      id: 1,
      name: "Computer Science",
      head: "Dr. <PERSON>",
      students: 2500,
      faculty: 120,
      programs: ["Bachelor's", "Master's", "PhD"],
      budget: 2500000,
      status: "active"
    },
    {
      id: 2,
      name: "Engineering",
      head: "Dr. Fatima El-<PERSON>ouri",
      students: 3200,
      faculty: 180,
      programs: ["Bachelor's", "Master's", "PhD"],
      budget: 3800000,
      status: "active"
    },
    {
      id: 3,
      name: "Business Administration",
      head: "Dr. Omar Zaki",
      students: 2800,
      faculty: 95,
      programs: ["Bachelor's", "Master's", "MBA"],
      budget: 2200000,
      status: "active"
    },
    {
      id: 4,
      name: "Medicine",
      head: "Dr. Aicha Benkirane",
      students: 1800,
      faculty: 150,
      programs: ["Bachelor's", "Master's", "MD"],
      budget: 4500000,
      status: "active"
    },
    {
      id: 5,
      name: "Arts & Literature",
      head: "Dr. Hassan Alami",
      students: 1200,
      faculty: 85,
      programs: ["Bachelor's", "Master's"],
      budget: 1200000,
      status: "active"
    }
  ];

  // Mock data for recent activities
  const recentActivities = [
    { id: 1, type: "enrollment", description: "150 new students enrolled in Computer Science", time: "2 hours ago" },
    { id: 2, type: "faculty", description: "Dr. Sarah Johnson joined Engineering department", time: "1 day ago" },
    { id: 3, type: "program", description: "New AI specialization program approved", time: "3 days ago" },
    { id: 4, type: "budget", description: "Q2 budget allocation completed", time: "1 week ago" }
  ];

  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.head.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{university.name}</h1>
              <p className="text-gray-600">University Management Dashboard</p>
            </div>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Department
            </Button>
          </div>
        </div>

        {/* University Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">{university.totalStudents.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div className="mt-4">
                <Progress value={75} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">75% capacity</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Faculty Members</p>
                  <p className="text-2xl font-bold">{university.totalFaculty}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-green-600" />
              </div>
              <div className="mt-4">
                <Progress value={85} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">85% positions filled</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Departments</p>
                  <p className="text-2xl font-bold">{university.totalDepartments}</p>
                </div>
                <Building className="w-8 h-8 text-purple-600" />
              </div>
              <div className="mt-4">
                <Badge variant="secondary">All Active</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Accreditation</p>
                  <p className="text-lg font-bold">{university.accreditation}</p>
                </div>
                <Award className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="mt-4">
                <Badge className="bg-green-100 text-green-800">Certified</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="departments" className="space-y-6">
          <TabsList>
            <TabsTrigger value="departments">Departments</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="activities">Recent Activities</TabsTrigger>
          </TabsList>

          <TabsContent value="departments" className="space-y-6">
            {/* Search */}
            <Card>
              <CardContent className="pt-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search departments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Departments Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredDepartments.map((department) => (
                <Card key={department.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{department.name}</CardTitle>
                        <CardDescription>Head: {department.head}</CardDescription>
                      </div>
                      <Badge className="bg-green-100 text-green-800">{department.status}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Students:</span>
                          <p className="font-medium">{department.students.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Faculty:</span>
                          <p className="font-medium">{department.faculty}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Budget:</span>
                          <p className="font-medium">${department.budget.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Programs:</span>
                          <p className="font-medium">{department.programs.length}</p>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500 text-sm">Programs Offered:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {department.programs.map((program, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {program}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Enrollment Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Fall 2024</span>
                      <span className="font-medium">15,000 students</span>
                    </div>
                    <Progress value={100} />
                    <div className="flex justify-between items-center">
                      <span>Spring 2024</span>
                      <span className="font-medium">14,200 students</span>
                    </div>
                    <Progress value={95} />
                    <div className="flex justify-between items-center">
                      <span>Fall 2023</span>
                      <span className="font-medium">13,800 students</span>
                    </div>
                    <Progress value={92} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Department Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {departments.slice(0, 4).map((dept) => (
                      <div key={dept.id} className="flex justify-between items-center">
                        <span className="text-sm">{dept.name}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={(dept.students / 3500) * 100} className="w-20 h-2" />
                          <span className="text-sm font-medium">{dept.students}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
