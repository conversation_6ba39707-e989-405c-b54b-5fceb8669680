"use client";
import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  BookOpen, 
  School, 
  GraduationCap,
  Calendar,
  Heart,
  Search,
  Clock,
  Star,
  Activity
} from "lucide-react";

export default function ElementarySchoolManagement() {
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data for elementary school
  const school = {
    name: "Green Valley Elementary",
    location: "Marrakech, Morocco",
    established: "2010",
    totalStudents: 450,
    totalTeachers: 25,
    totalClasses: 18,
    satisfactionRate: 95
  };

  // Mock data for grades
  const grades = [
    {
      id: 1,
      grade: "Kindergarten",
      sections: ["KG-A", "KG-B", "KG-C"],
      students: 75,
      teachers: 4,
      subjects: ["Basic Math", "Reading", "Art", "Music", "PE", "Science Exploration"],
      coordinator: "Ms. Amina Benali",
      ageRange: "4-5 years"
    },
    {
      id: 2,
      grade: "Grade 1",
      sections: ["1A", "1B", "1C"],
      students: 72,
      teachers: 4,
      subjects: ["Math", "Reading", "Writing", "Science", "Art", "Music", "PE"],
      coordinator: "Mr. Hassan Alami",
      ageRange: "6-7 years"
    },
    {
      id: 3,
      grade: "Grade 2",
      sections: ["2A", "2B", "2C"],
      students: 68,
      teachers: 4,
      subjects: ["Math", "Reading", "Writing", "Science", "Social Studies", "Art", "Music", "PE"],
      coordinator: "Ms. Fatima El-Mansouri",
      ageRange: "7-8 years"
    },
    {
      id: 4,
      grade: "Grade 3",
      sections: ["3A", "3B", "3C"],
      students: 65,
      teachers: 4,
      subjects: ["Math", "Reading", "Writing", "Science", "Social Studies", "Art", "Music", "PE"],
      coordinator: "Mr. Omar Zaki",
      ageRange: "8-9 years"
    },
    {
      id: 5,
      grade: "Grade 4",
      sections: ["4A", "4B", "4C"],
      students: 62,
      teachers: 4,
      subjects: ["Math", "Reading", "Writing", "Science", "Social Studies", "Art", "Music", "PE"],
      coordinator: "Ms. Aicha Benkirane",
      ageRange: "9-10 years"
    },
    {
      id: 6,
      grade: "Grade 5",
      sections: ["5A", "5B", "5C"],
      students: 58,
      teachers: 4,
      subjects: ["Math", "Reading", "Writing", "Science", "Social Studies", "Art", "Music", "PE"],
      coordinator: "Mr. Ahmed Benali",
      ageRange: "10-11 years"
    }
  ];

  // Mock data for activities
  const activities = [
    { id: 1, name: "Art Club", participants: 45, teacher: "Ms. Sarah Johnson", day: "Monday" },
    { id: 2, name: "Science Club", participants: 38, teacher: "Mr. Hassan Alami", day: "Tuesday" },
    { id: 3, name: "Music Band", participants: 32, teacher: "Ms. Amina Benali", day: "Wednesday" },
    { id: 4, name: "Sports Club", participants: 55, teacher: "Mr. Omar Zaki", day: "Thursday" },
    { id: 5, name: "Reading Club", participants: 42, teacher: "Ms. Fatima El-Mansouri", day: "Friday" }
  ];

  // Mock data for upcoming events
  const upcomingEvents = [
    { id: 1, title: "Parent Open Day", date: "2024-02-15", type: "meeting" },
    { id: 2, title: "Art Exhibition", date: "2024-02-20", type: "event" },
    { id: 3, title: "Sports Day", date: "2024-02-25", type: "sports" },
    { id: 4, title: "Science Fair", date: "2024-03-05", type: "academic" },
    { id: 5, title: "Spring Concert", date: "2024-03-15", type: "performance" }
  ];

  const filteredGrades = grades.filter(grade =>
    grade.grade.toLowerCase().includes(searchTerm.toLowerCase()) ||
    grade.coordinator.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/test-pages">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Test Pages
            </Button>
          </Link>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{school.name}</h1>
              <p className="text-gray-600">Elementary School Management Dashboard</p>
            </div>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Grade
            </Button>
          </div>
        </div>

        {/* School Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold">{school.totalStudents}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <div className="mt-4">
                <Progress value={90} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">90% capacity</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Teachers</p>
                  <p className="text-2xl font-bold">{school.totalTeachers}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-green-600" />
              </div>
              <div className="mt-4">
                <Badge variant="secondary">Certified Staff</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Classes</p>
                  <p className="text-2xl font-bold">{school.totalClasses}</p>
                </div>
                <School className="w-8 h-8 text-purple-600" />
              </div>
              <div className="mt-4">
                <Badge className="bg-blue-100 text-blue-800">6 Grades</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                  <p className="text-2xl font-bold">{school.satisfactionRate}%</p>
                </div>
                <Heart className="w-8 h-8 text-red-500" />
              </div>
              <div className="mt-4">
                <Progress value={school.satisfactionRate} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="grades" className="space-y-6">
          <TabsList>
            <TabsTrigger value="grades">Grades & Classes</TabsTrigger>
            <TabsTrigger value="activities">Activities</TabsTrigger>
            <TabsTrigger value="events">Upcoming Events</TabsTrigger>
          </TabsList>

          <TabsContent value="grades" className="space-y-6">
            {/* Search */}
            <Card>
              <CardContent className="pt-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search grades or coordinators..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Grades Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredGrades.map((gradeItem) => (
                <Card key={gradeItem.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{gradeItem.grade}</CardTitle>
                        <CardDescription>
                          Coordinator: {gradeItem.coordinator} • Ages: {gradeItem.ageRange}
                        </CardDescription>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Students:</span>
                          <p className="font-medium">{gradeItem.students}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Teachers:</span>
                          <p className="font-medium">{gradeItem.teachers}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Sections:</span>
                          <p className="font-medium">{gradeItem.sections.length}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Subjects:</span>
                          <p className="font-medium">{gradeItem.subjects.length}</p>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500 text-sm">Sections:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {gradeItem.sections.map((section, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500 text-sm">Core Subjects:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {gradeItem.subjects.slice(0, 4).map((subject, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {subject}
                            </Badge>
                          ))}
                          {gradeItem.subjects.length > 4 && (
                            <Badge variant="outline" className="text-xs">
                              +{gradeItem.subjects.length - 4} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="activities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Extracurricular Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {activities.map((activity) => (
                    <div key={activity.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{activity.name}</h4>
                        <Badge variant="outline">{activity.day}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">Teacher: {activity.teacher}</p>
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm">{activity.participants} participants</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Clock className="w-5 h-5 text-gray-400" />
                      <div className="flex-1">
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-gray-500">{event.date}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {event.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
