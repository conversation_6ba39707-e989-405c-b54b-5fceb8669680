"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import AdminLayout from "@/components/AdminLayout";
import {
  School,
  ShoppingCart,
  Users,
  BarChart3,
  TrendingUp
} from "lucide-react";

export default function AdminDashboard() {

  // Mock data for dashboard overview
  const dashboardStats = {
    schools: {
      total: 45,
      active: 42,
      pending: 3,
      revenue: 2450000
    },
    marketplace: {
      courses: 1250,
      instructors: 380,
      students: 15600,
      revenue: 890000
    },
    users: {
      total: 18500,
      active: 16200,
      newThisMonth: 450
    }
  };

  return (
    <AdminLayout>
      <div className="p-6">
          {/* Platform Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Schools</CardTitle>
                <School className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.schools.total}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardStats.schools.active} active, {dashboardStats.schools.pending} pending
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Marketplace Courses</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.marketplace.courses.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardStats.marketplace.instructors} instructors
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.users.total.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  +{dashboardStats.users.newThisMonth} this month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${((dashboardStats.schools.revenue + dashboardStats.marketplace.revenue) / 1000000).toFixed(1)}M
                </div>
                <p className="text-xs text-muted-foreground">
                  Combined platforms
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Platform Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Jangua Schools Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <School className="h-5 w-5" />
                  Jangua Schools
                </CardTitle>
                <CardDescription>Educational institution management platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Revenue</span>
                    <span className="text-lg font-bold">${(dashboardStats.schools.revenue / 1000000).toFixed(1)}M</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Active Schools:</span>
                      <p className="font-medium">{dashboardStats.schools.active}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Pending:</span>
                      <p className="font-medium">{dashboardStats.schools.pending}</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link href="/schools">
                      <Button size="sm" className="flex-1">Manage Schools</Button>
                    </Link>
                    <Link href="/schools/reports">
                      <Button size="sm" variant="outline">View Reports</Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Jangua Marketplace Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Jangua Marketplace
                </CardTitle>
                <CardDescription>E-learning marketplace platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Revenue</span>
                    <span className="text-lg font-bold">${(dashboardStats.marketplace.revenue / 1000).toFixed(0)}K</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Courses:</span>
                      <p className="font-medium">{dashboardStats.marketplace.courses.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Students:</span>
                      <p className="font-medium">{dashboardStats.marketplace.students.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link href="/marketplace">
                      <Button size="sm" className="flex-1">Manage Marketplace</Button>
                    </Link>
                    <Link href="/marketplace/analytics">
                      <Button size="sm" variant="outline">View Analytics</Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link href="/schools/institutions">
                  <Button variant="outline" className="w-full h-20 flex-col">
                    <School className="h-6 w-6 mb-2" />
                    <span className="text-sm">Add School</span>
                  </Button>
                </Link>
                <Link href="/marketplace/courses">
                  <Button variant="outline" className="w-full h-20 flex-col">
                    <ShoppingCart className="h-6 w-6 mb-2" />
                    <span className="text-sm">Review Courses</span>
                  </Button>
                </Link>
                <Link href="/users/all">
                  <Button variant="outline" className="w-full h-20 flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    <span className="text-sm">Manage Users</span>
                  </Button>
                </Link>
                <Link href="/analytics/overview">
                  <Button variant="outline" className="w-full h-20 flex-col">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    <span className="text-sm">View Analytics</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
      
      </div>
    </AdminLayout>
  );
}
