import type * as ts from 'typescript/lib/tsserverlibrary';
/**
 * Parses a TSConfig file using the same logic as tsserver.
 *
 * @param configFile the path to the tsconfig.json file, relative to `projectDirectory`
 * @param projectDirectory the project directory to use as the CWD, defaults to `process.cwd()`
 */
export declare function getParsedConfigFile(tsserver: typeof ts, configFile: string, projectDirectory?: string): ts.ParsedCommandLine;
//# sourceMappingURL=getParsedConfigFile.d.ts.map